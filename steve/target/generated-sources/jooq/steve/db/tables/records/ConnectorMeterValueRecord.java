/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.ConnectorMeterValue;

import org.joda.time.DateTime;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorMeterValueRecord extends TableRecordImpl<ConnectorMeterValueRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.connector_meter_value.connector_pk</code>.
     */
    public ConnectorMeterValueRecord setConnectorPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.transaction_pk</code>.
     */
    public ConnectorMeterValueRecord setTransactionPk(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.transaction_pk</code>.
     */
    public Integer getTransactionPk() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.value_timestamp</code>.
     */
    public ConnectorMeterValueRecord setValueTimestamp(DateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.value_timestamp</code>.
     */
    public DateTime getValueTimestamp() {
        return (DateTime) get(2);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.value</code>.
     */
    public ConnectorMeterValueRecord setValue(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.value</code>.
     */
    public String getValue() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.reading_context</code>.
     */
    public ConnectorMeterValueRecord setReadingContext(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.reading_context</code>.
     */
    public String getReadingContext() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.format</code>.
     */
    public ConnectorMeterValueRecord setFormat(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.format</code>.
     */
    public String getFormat() {
        return (String) get(5);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.measurand</code>.
     */
    public ConnectorMeterValueRecord setMeasurand(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.measurand</code>.
     */
    public String getMeasurand() {
        return (String) get(6);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.location</code>.
     */
    public ConnectorMeterValueRecord setLocation(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.location</code>.
     */
    public String getLocation() {
        return (String) get(7);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.unit</code>.
     */
    public ConnectorMeterValueRecord setUnit(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.unit</code>.
     */
    public String getUnit() {
        return (String) get(8);
    }

    /**
     * Setter for <code>stevedb.connector_meter_value.phase</code>.
     */
    public ConnectorMeterValueRecord setPhase(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_meter_value.phase</code>.
     */
    public String getPhase() {
        return (String) get(9);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConnectorMeterValueRecord
     */
    public ConnectorMeterValueRecord() {
        super(ConnectorMeterValue.CONNECTOR_METER_VALUE);
    }

    /**
     * Create a detached, initialised ConnectorMeterValueRecord
     */
    public ConnectorMeterValueRecord(Integer connectorPk, Integer transactionPk, DateTime valueTimestamp, String value, String readingContext, String format, String measurand, String location, String unit, String phase) {
        super(ConnectorMeterValue.CONNECTOR_METER_VALUE);

        setConnectorPk(connectorPk);
        setTransactionPk(transactionPk);
        setValueTimestamp(valueTimestamp);
        setValue(value);
        setReadingContext(readingContext);
        setFormat(format);
        setMeasurand(measurand);
        setLocation(location);
        setUnit(unit);
        setPhase(phase);
        resetChangedOnNotNull();
    }
}
