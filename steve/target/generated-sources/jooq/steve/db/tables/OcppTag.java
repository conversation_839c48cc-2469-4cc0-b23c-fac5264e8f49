/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Indexes;
import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.OcppTag.OcppTagPath;
import jooq.steve.db.tables.Reservation.ReservationPath;
import jooq.steve.db.tables.TransactionStart.TransactionStartPath;
import jooq.steve.db.tables.User.UserPath;
import jooq.steve.db.tables.records.OcppTagRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OcppTag extends TableImpl<OcppTagRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.ocpp_tag</code>
     */
    public static final OcppTag OCPP_TAG = new OcppTag();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OcppTagRecord> getRecordType() {
        return OcppTagRecord.class;
    }

    /**
     * The column <code>stevedb.ocpp_tag.ocpp_tag_pk</code>.
     */
    public final TableField<OcppTagRecord, Integer> OCPP_TAG_PK = createField(DSL.name("ocpp_tag_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.ocpp_tag.id_tag</code>.
     */
    public final TableField<OcppTagRecord, String> ID_TAG = createField(DSL.name("id_tag"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.ocpp_tag.parent_id_tag</code>.
     */
    public final TableField<OcppTagRecord, String> PARENT_ID_TAG = createField(DSL.name("parent_id_tag"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag.expiry_date</code>.
     */
    public final TableField<OcppTagRecord, DateTime> EXPIRY_DATE = createField(DSL.name("expiry_date"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.ocpp_tag.max_active_transaction_count</code>.
     */
    public final TableField<OcppTagRecord, Integer> MAX_ACTIVE_TRANSACTION_COUNT = createField(DSL.name("max_active_transaction_count"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag.note</code>.
     */
    public final TableField<OcppTagRecord, String> NOTE = createField(DSL.name("note"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    private OcppTag(Name alias, Table<OcppTagRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private OcppTag(Name alias, Table<OcppTagRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.ocpp_tag</code> table reference
     */
    public OcppTag(String alias) {
        this(DSL.name(alias), OCPP_TAG);
    }

    /**
     * Create an aliased <code>stevedb.ocpp_tag</code> table reference
     */
    public OcppTag(Name alias) {
        this(alias, OCPP_TAG);
    }

    /**
     * Create a <code>stevedb.ocpp_tag</code> table reference
     */
    public OcppTag() {
        this(DSL.name("ocpp_tag"), null);
    }

    public <O extends Record> OcppTag(Table<O> path, ForeignKey<O, OcppTagRecord> childPath, InverseForeignKey<O, OcppTagRecord> parentPath) {
        super(path, childPath, parentPath, OCPP_TAG);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class OcppTagPath extends OcppTag implements Path<OcppTagRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> OcppTagPath(Table<O> path, ForeignKey<O, OcppTagRecord> childPath, InverseForeignKey<O, OcppTagRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private OcppTagPath(Name alias, Table<OcppTagRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public OcppTagPath as(String alias) {
            return new OcppTagPath(DSL.name(alias), this);
        }

        @Override
        public OcppTagPath as(Name alias) {
            return new OcppTagPath(alias, this);
        }

        @Override
        public OcppTagPath as(Table<?> alias) {
            return new OcppTagPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.OCPP_TAG_USER_EXPIRYDATE_IDX);
    }

    @Override
    public Identity<OcppTagRecord, Integer> getIdentity() {
        return (Identity<OcppTagRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<OcppTagRecord> getPrimaryKey() {
        return Keys.KEY_OCPP_TAG_PRIMARY;
    }

    @Override
    public List<UniqueKey<OcppTagRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_OCPP_TAG_IDTAG_UNIQUE);
    }

    @Override
    public List<ForeignKey<OcppTagRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_OCPP_TAG_PARENT_ID_TAG);
    }

    private transient OcppTagPath _ocppTag;

    /**
     * Get the implicit join path to the <code>stevedb.ocpp_tag</code> table.
     */
    public OcppTagPath ocppTag() {
        if (_ocppTag == null)
            _ocppTag = new OcppTagPath(this, Keys.FK_OCPP_TAG_PARENT_ID_TAG, null);

        return _ocppTag;
    }

    private transient ReservationPath _reservation;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.reservation</code> table
     */
    public ReservationPath reservation() {
        if (_reservation == null)
            _reservation = new ReservationPath(this, null, Keys.FK_RESERVATION_OCPP_TAG_ID_TAG.getInverseKey());

        return _reservation;
    }

    private transient TransactionStartPath _transactionStart;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.transaction_start</code> table
     */
    public TransactionStartPath transactionStart() {
        if (_transactionStart == null)
            _transactionStart = new TransactionStartPath(this, null, Keys.FK_TRANSACTION_OCPP_TAG_ID_TAG.getInverseKey());

        return _transactionStart;
    }

    private transient UserPath _user;

    /**
     * Get the implicit to-many join path to the <code>stevedb.user</code> table
     */
    public UserPath user() {
        if (_user == null)
            _user = new UserPath(this, null, Keys.FK_USER_OCPP_TAG_OTPK.getInverseKey());

        return _user;
    }

    @Override
    public OcppTag as(String alias) {
        return new OcppTag(DSL.name(alias), this);
    }

    @Override
    public OcppTag as(Name alias) {
        return new OcppTag(alias, this);
    }

    @Override
    public OcppTag as(Table<?> alias) {
        return new OcppTag(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public OcppTag rename(String name) {
        return new OcppTag(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public OcppTag rename(Name name) {
        return new OcppTag(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public OcppTag rename(Table<?> name) {
        return new OcppTag(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTag where(Condition condition) {
        return new OcppTag(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTag where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTag where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTag where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTag where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTag where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTag where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTag where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTag whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTag whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
