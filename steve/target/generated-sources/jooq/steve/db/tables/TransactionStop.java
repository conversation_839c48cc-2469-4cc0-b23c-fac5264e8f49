/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.enums.TransactionStopEventActor;
import jooq.steve.db.tables.TransactionStart.TransactionStartPath;
import jooq.steve.db.tables.records.TransactionStopRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionStop extends TableImpl<TransactionStopRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.transaction_stop</code>
     */
    public static final TransactionStop TRANSACTION_STOP = new TransactionStop();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TransactionStopRecord> getRecordType() {
        return TransactionStopRecord.class;
    }

    /**
     * The column <code>stevedb.transaction_stop.transaction_pk</code>.
     */
    public final TableField<TransactionStopRecord, Integer> TRANSACTION_PK = createField(DSL.name("transaction_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column <code>stevedb.transaction_stop.event_timestamp</code>.
     */
    public final TableField<TransactionStopRecord, DateTime> EVENT_TIMESTAMP = createField(DSL.name("event_timestamp"), SQLDataType.TIMESTAMP(6).nullable(false).defaultValue(DSL.field(DSL.raw("current_timestamp(6)"), SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.transaction_stop.event_actor</code>.
     */
    public final TableField<TransactionStopRecord, TransactionStopEventActor> EVENT_ACTOR = createField(DSL.name("event_actor"), SQLDataType.VARCHAR(7).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)).asEnumDataType(TransactionStopEventActor.class), this, "");

    /**
     * The column <code>stevedb.transaction_stop.stop_timestamp</code>.
     */
    public final TableField<TransactionStopRecord, DateTime> STOP_TIMESTAMP = createField(DSL.name("stop_timestamp"), SQLDataType.TIMESTAMP(6).nullable(false).defaultValue(DSL.field(DSL.raw("current_timestamp(6)"), SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.transaction_stop.stop_value</code>.
     */
    public final TableField<TransactionStopRecord, String> STOP_VALUE = createField(DSL.name("stop_value"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.transaction_stop.stop_reason</code>.
     */
    public final TableField<TransactionStopRecord, String> STOP_REASON = createField(DSL.name("stop_reason"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    private TransactionStop(Name alias, Table<TransactionStopRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private TransactionStop(Name alias, Table<TransactionStopRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.transaction_stop</code> table reference
     */
    public TransactionStop(String alias) {
        this(DSL.name(alias), TRANSACTION_STOP);
    }

    /**
     * Create an aliased <code>stevedb.transaction_stop</code> table reference
     */
    public TransactionStop(Name alias) {
        this(alias, TRANSACTION_STOP);
    }

    /**
     * Create a <code>stevedb.transaction_stop</code> table reference
     */
    public TransactionStop() {
        this(DSL.name("transaction_stop"), null);
    }

    public <O extends Record> TransactionStop(Table<O> path, ForeignKey<O, TransactionStopRecord> childPath, InverseForeignKey<O, TransactionStopRecord> parentPath) {
        super(path, childPath, parentPath, TRANSACTION_STOP);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class TransactionStopPath extends TransactionStop implements Path<TransactionStopRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> TransactionStopPath(Table<O> path, ForeignKey<O, TransactionStopRecord> childPath, InverseForeignKey<O, TransactionStopRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private TransactionStopPath(Name alias, Table<TransactionStopRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public TransactionStopPath as(String alias) {
            return new TransactionStopPath(DSL.name(alias), this);
        }

        @Override
        public TransactionStopPath as(Name alias) {
            return new TransactionStopPath(alias, this);
        }

        @Override
        public TransactionStopPath as(Table<?> alias) {
            return new TransactionStopPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public UniqueKey<TransactionStopRecord> getPrimaryKey() {
        return Keys.KEY_TRANSACTION_STOP_PRIMARY;
    }

    @Override
    public List<ForeignKey<TransactionStopRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_TRANSACTION_STOP_TRANSACTION_PK);
    }

    private transient TransactionStartPath _transactionStart;

    /**
     * Get the implicit join path to the <code>stevedb.transaction_start</code>
     * table.
     */
    public TransactionStartPath transactionStart() {
        if (_transactionStart == null)
            _transactionStart = new TransactionStartPath(this, Keys.FK_TRANSACTION_STOP_TRANSACTION_PK, null);

        return _transactionStart;
    }

    @Override
    public TransactionStop as(String alias) {
        return new TransactionStop(DSL.name(alias), this);
    }

    @Override
    public TransactionStop as(Name alias) {
        return new TransactionStop(alias, this);
    }

    @Override
    public TransactionStop as(Table<?> alias) {
        return new TransactionStop(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStop rename(String name) {
        return new TransactionStop(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStop rename(Name name) {
        return new TransactionStop(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStop rename(Table<?> name) {
        return new TransactionStop(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStop where(Condition condition) {
        return new TransactionStop(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStop where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStop where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStop where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStop where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStop where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStop where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStop where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStop whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStop whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
