/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.records.WebUserRecord;

import org.jooq.Check;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.JSON;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class WebUser extends TableImpl<WebUserRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.web_user</code>
     */
    public static final WebUser WEB_USER = new WebUser();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<WebUserRecord> getRecordType() {
        return WebUserRecord.class;
    }

    /**
     * The column <code>stevedb.web_user.web_user_pk</code>.
     */
    public final TableField<WebUserRecord, Integer> WEB_USER_PK = createField(DSL.name("web_user_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.web_user.username</code>.
     */
    public final TableField<WebUserRecord, String> USERNAME = createField(DSL.name("username"), SQLDataType.VARCHAR(500).nullable(false), this, "");

    /**
     * The column <code>stevedb.web_user.password</code>.
     */
    public final TableField<WebUserRecord, String> PASSWORD = createField(DSL.name("password"), SQLDataType.VARCHAR(500).nullable(false), this, "");

    /**
     * The column <code>stevedb.web_user.api_password</code>.
     */
    public final TableField<WebUserRecord, String> API_PASSWORD = createField(DSL.name("api_password"), SQLDataType.VARCHAR(500).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.web_user.enabled</code>.
     */
    public final TableField<WebUserRecord, Boolean> ENABLED = createField(DSL.name("enabled"), SQLDataType.BOOLEAN.nullable(false), this, "");

    /**
     * The column <code>stevedb.web_user.authorities</code>.
     */
    public final TableField<WebUserRecord, JSON> AUTHORITIES = createField(DSL.name("authorities"), SQLDataType.JSON.nullable(false), this, "");

    private WebUser(Name alias, Table<WebUserRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private WebUser(Name alias, Table<WebUserRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.web_user</code> table reference
     */
    public WebUser(String alias) {
        this(DSL.name(alias), WEB_USER);
    }

    /**
     * Create an aliased <code>stevedb.web_user</code> table reference
     */
    public WebUser(Name alias) {
        this(alias, WEB_USER);
    }

    /**
     * Create a <code>stevedb.web_user</code> table reference
     */
    public WebUser() {
        this(DSL.name("web_user"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public Identity<WebUserRecord, Integer> getIdentity() {
        return (Identity<WebUserRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<WebUserRecord> getPrimaryKey() {
        return Keys.KEY_WEB_USER_PRIMARY;
    }

    @Override
    public List<UniqueKey<WebUserRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_WEB_USER_USERNAME);
    }

    @Override
    public List<Check<WebUserRecord>> getChecks() {
        return Arrays.asList(
            Internal.createCheck(this, DSL.name("authorities"), "json_valid(`authorities`)", true),
            Internal.createCheck(this, DSL.name("authorities_must_be_array"), "json_type(`authorities`) = convert('ARRAY' using utf8)", true)
        );
    }

    @Override
    public WebUser as(String alias) {
        return new WebUser(DSL.name(alias), this);
    }

    @Override
    public WebUser as(Name alias) {
        return new WebUser(alias, this);
    }

    @Override
    public WebUser as(Table<?> alias) {
        return new WebUser(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public WebUser rename(String name) {
        return new WebUser(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public WebUser rename(Name name) {
        return new WebUser(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public WebUser rename(Table<?> name) {
        return new WebUser(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public WebUser where(Condition condition) {
        return new WebUser(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public WebUser where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public WebUser where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public WebUser where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public WebUser where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public WebUser where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public WebUser where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public WebUser where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public WebUser whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public WebUser whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
