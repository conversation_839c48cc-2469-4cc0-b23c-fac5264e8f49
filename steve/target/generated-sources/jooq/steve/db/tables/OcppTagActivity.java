/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Collection;

import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.records.OcppTagActivityRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OcppTagActivity extends TableImpl<OcppTagActivityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.ocpp_tag_activity</code>
     */
    public static final OcppTagActivity OCPP_TAG_ACTIVITY = new OcppTagActivity();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OcppTagActivityRecord> getRecordType() {
        return OcppTagActivityRecord.class;
    }

    /**
     * The column <code>stevedb.ocpp_tag_activity.ocpp_tag_pk</code>.
     */
    public final TableField<OcppTagActivityRecord, Integer> OCPP_TAG_PK = createField(DSL.name("ocpp_tag_pk"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag_activity.id_tag</code>.
     */
    public final TableField<OcppTagActivityRecord, String> ID_TAG = createField(DSL.name("id_tag"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.ocpp_tag_activity.parent_id_tag</code>.
     */
    public final TableField<OcppTagActivityRecord, String> PARENT_ID_TAG = createField(DSL.name("parent_id_tag"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag_activity.expiry_date</code>.
     */
    public final TableField<OcppTagActivityRecord, DateTime> EXPIRY_DATE = createField(DSL.name("expiry_date"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column
     * <code>stevedb.ocpp_tag_activity.max_active_transaction_count</code>.
     */
    public final TableField<OcppTagActivityRecord, Integer> MAX_ACTIVE_TRANSACTION_COUNT = createField(DSL.name("max_active_transaction_count"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("1", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag_activity.note</code>.
     */
    public final TableField<OcppTagActivityRecord, String> NOTE = createField(DSL.name("note"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    /**
     * The column
     * <code>stevedb.ocpp_tag_activity.active_transaction_count</code>.
     */
    public final TableField<OcppTagActivityRecord, Long> ACTIVE_TRANSACTION_COUNT = createField(DSL.name("active_transaction_count"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag_activity.in_transaction</code>.
     */
    public final TableField<OcppTagActivityRecord, Boolean> IN_TRANSACTION = createField(DSL.name("in_transaction"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>stevedb.ocpp_tag_activity.blocked</code>.
     */
    public final TableField<OcppTagActivityRecord, Boolean> BLOCKED = createField(DSL.name("blocked"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BOOLEAN)), this, "");

    private OcppTagActivity(Name alias, Table<OcppTagActivityRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private OcppTagActivity(Name alias, Table<OcppTagActivityRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view `ocpp_tag_activity` as select `o`.`ocpp_tag_pk` AS `ocpp_tag_pk`,`o`.`id_tag` AS `id_tag`,`o`.`parent_id_tag` AS `parent_id_tag`,`o`.`expiry_date` AS `expiry_date`,`o`.`max_active_transaction_count` AS `max_active_transaction_count`,`o`.`note` AS `note`,count(`t`.`id_tag`) AS `active_transaction_count`,case when count(`t`.`id_tag`) > 0 then 1 else 0 end AS `in_transaction`,case when `o`.`max_active_transaction_count` = 0 then 1 else 0 end AS `blocked` from (`stevedb`.`ocpp_tag` `o` left join `stevedb`.`transaction` `t` on(`o`.`id_tag` = `t`.`id_tag` and `t`.`stop_timestamp` is null and `t`.`stop_value` is null)) group by `o`.`ocpp_tag_pk`,`o`.`parent_id_tag`,`o`.`expiry_date`,`o`.`max_active_transaction_count`,`o`.`note`"), where);
    }

    /**
     * Create an aliased <code>stevedb.ocpp_tag_activity</code> table reference
     */
    public OcppTagActivity(String alias) {
        this(DSL.name(alias), OCPP_TAG_ACTIVITY);
    }

    /**
     * Create an aliased <code>stevedb.ocpp_tag_activity</code> table reference
     */
    public OcppTagActivity(Name alias) {
        this(alias, OCPP_TAG_ACTIVITY);
    }

    /**
     * Create a <code>stevedb.ocpp_tag_activity</code> table reference
     */
    public OcppTagActivity() {
        this(DSL.name("ocpp_tag_activity"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public OcppTagActivity as(String alias) {
        return new OcppTagActivity(DSL.name(alias), this);
    }

    @Override
    public OcppTagActivity as(Name alias) {
        return new OcppTagActivity(alias, this);
    }

    @Override
    public OcppTagActivity as(Table<?> alias) {
        return new OcppTagActivity(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public OcppTagActivity rename(String name) {
        return new OcppTagActivity(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public OcppTagActivity rename(Name name) {
        return new OcppTagActivity(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public OcppTagActivity rename(Table<?> name) {
        return new OcppTagActivity(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTagActivity where(Condition condition) {
        return new OcppTagActivity(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTagActivity where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTagActivity where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTagActivity where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTagActivity where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTagActivity where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTagActivity where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public OcppTagActivity where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTagActivity whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public OcppTagActivity whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
