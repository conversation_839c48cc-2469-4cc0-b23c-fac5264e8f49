/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.enums.TransactionStopEventActor;
import jooq.steve.db.tables.TransactionStop;

import org.joda.time.DateTime;
import org.jooq.Record2;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionStopRecord extends UpdatableRecordImpl<TransactionStopRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.transaction_stop.transaction_pk</code>.
     */
    public TransactionStopRecord setTransactionPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop.transaction_pk</code>.
     */
    public Integer getTransactionPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.transaction_stop.event_timestamp</code>.
     */
    public TransactionStopRecord setEventTimestamp(DateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop.event_timestamp</code>.
     */
    public DateTime getEventTimestamp() {
        return (DateTime) get(1);
    }

    /**
     * Setter for <code>stevedb.transaction_stop.event_actor</code>.
     */
    public TransactionStopRecord setEventActor(TransactionStopEventActor value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop.event_actor</code>.
     */
    public TransactionStopEventActor getEventActor() {
        return (TransactionStopEventActor) get(2);
    }

    /**
     * Setter for <code>stevedb.transaction_stop.stop_timestamp</code>.
     */
    public TransactionStopRecord setStopTimestamp(DateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop.stop_timestamp</code>.
     */
    public DateTime getStopTimestamp() {
        return (DateTime) get(3);
    }

    /**
     * Setter for <code>stevedb.transaction_stop.stop_value</code>.
     */
    public TransactionStopRecord setStopValue(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop.stop_value</code>.
     */
    public String getStopValue() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.transaction_stop.stop_reason</code>.
     */
    public TransactionStopRecord setStopReason(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop.stop_reason</code>.
     */
    public String getStopReason() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record2<Integer, DateTime> key() {
        return (Record2) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TransactionStopRecord
     */
    public TransactionStopRecord() {
        super(TransactionStop.TRANSACTION_STOP);
    }

    /**
     * Create a detached, initialised TransactionStopRecord
     */
    public TransactionStopRecord(Integer transactionPk, DateTime eventTimestamp, TransactionStopEventActor eventActor, DateTime stopTimestamp, String stopValue, String stopReason) {
        super(TransactionStop.TRANSACTION_STOP);

        setTransactionPk(transactionPk);
        setEventTimestamp(eventTimestamp);
        setEventActor(eventActor);
        setStopTimestamp(stopTimestamp);
        setStopValue(stopValue);
        setStopReason(stopReason);
        resetChangedOnNotNull();
    }
}
