<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Puppy Crawl//DTD Check Configuration 1.2//EN"
    "http://www.puppycrawl.com/dtds/configuration_1_2.dtd">

<module name="Checker">

    <!-- Checks that each Java package has a Javadoc file used for commenting. -->
    <!-- See http://checkstyle.sf.net/config_javadoc.html#JavadocPackage       -->
    <!--<module name="JavadocPackage">-->
        <!--<property name="allowLegacy" value="true"/>-->
    <!--</module>-->

    <!-- Checks whether files end with a new line.                        -->
    <!-- See http://checkstyle.sf.net/config_misc.html#NewlineAtEndOfFile -->
    <module name="NewlineAtEndOfFile"/>

    <!-- Checks that property files contain the same keys.         -->
    <!-- See http://checkstyle.sf.net/config_misc.html#Translation -->
    <module name="Translation"/>

    <module name="FileLength"/>

    <!-- Following interprets the header file as regular expressions. -->
    <!-- <module name="RegexpHeader"/>                                -->

    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>

    <module name="RegexpSingleline">
        <!-- \s matches whitespace character, $ matches end of line. -->
        <property name="format" value="\s+$"/>
        <property name="message" value="Line has trailing spaces."/>
    </module>

    <!-- Checks for Size Violations.                    -->
    <!-- See http://checkstyle.sf.net/config_sizes.html -->
    <module name="LineLength">
        <property name="max" value="120"/>
    </module>

    <module name="TreeWalker">

        <!-- <property name="cacheFile" value="${checkstyle.cache.file}"/> -->

        <!-- required for SuppressWarningsFilter (and other Suppress* rules not used here) -->
        <!-- see http://checkstyle.sourceforge.net/config_annotation.html#SuppressWarningsHolder -->
        <module name="SuppressWarningsHolder"/>

        <!-- Checks for Javadoc comments.                     -->
        <!-- See http://checkstyle.sf.net/config_javadoc.html -->
        <!--<module name="JavadocMethod"/>-->
        <!--<module name="JavadocType"/>-->
        <!--<module name="JavadocVariable"/>-->
        <!--<module name="JavadocStyle"/>-->


        <!-- Checks for Naming Conventions.                  -->
        <!-- See http://checkstyle.sf.net/config_naming.html -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>


        <!-- Checks for Headers                                -->
        <!-- See http://checkstyle.sf.net/config_header.html   -->
        <!-- <module name="Header">                            -->
            <!-- The follow property value demonstrates the ability     -->
            <!-- to have access to ANT properties. In this case it uses -->
            <!-- the ${basedir} property to allow Checkstyle to be run  -->
            <!-- from any directory within a project. See property      -->
            <!-- expansion,                                             -->
            <!-- http://checkstyle.sf.net/config.html#properties        -->
            <!-- <property                                              -->
            <!--     name="headerFile"                                  -->
            <!--     value="${basedir}/java.header"/>                   -->
        <!-- </module> -->


        <!-- Checks for imports                              -->
        <!-- See http://checkstyle.sf.net/config_import.html -->
        <module name="AvoidStarImport"/>
        <module name="IllegalImport"/> <!-- defaults to sun.* packages -->
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>


        <!-- Checks for Size Violations.                    -->
        <!-- See http://checkstyle.sf.net/config_sizes.html -->
        <module name="MethodLength"/>
        <module name="ParameterNumber"/>


        <!-- Checks for whitespace                               -->
        <!-- See http://checkstyle.sf.net/config_whitespace.html -->
        <module name="EmptyForIteratorPad"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>


        <!-- Modifier Checks                                    -->
        <!-- See http://checkstyle.sf.net/config_modifiers.html -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>


        <!-- Checks for blocks. You know, those {}'s         -->
        <!-- See http://checkstyle.sf.net/config_blocks.html -->
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock"/>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>


        <!-- Checks for common coding problems               -->
        <!-- See http://checkstyle.sf.net/config_coding.html -->
        <!-- <module name="AvoidInlineConditionals"/> -->
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <!-- <module name="HiddenField"/> -->
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <!--<module name="MagicNumber"/>-->
        <module name="MissingSwitchDefault"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>

        <!-- Checks for class design                         -->
        <!-- See http://checkstyle.sf.net/config_design.html -->
        <!--<module name="DesignForExtension"/>-->
        <module name="FinalClass"/>
        <module name="HideUtilityClassConstructor"/>
        <module name="InterfaceIsType"/>
        <module name="VisibilityModifier"/>


        <!-- Miscellaneous other checks.                   -->
        <!-- See http://checkstyle.sf.net/config_misc.html -->
        <module name="ArrayTypeStyle"/>
        <!--<module name="FinalParameters"/>-->
        <module name="TodoComment"/>
        <module name="UpperEll"/>

    </module>

    <!-- Support @SuppressWarnings (added in Checkstyle 5.7) -->
    <!-- see http://checkstyle.sourceforge.net/config.html#SuppressWarningsFilter -->
    <module name="SuppressWarningsFilter"/>

    <!-- Checks properties file for a duplicated properties. -->
    <!-- See http://checkstyle.sourceforge.net/config_misc.html#UniqueProperties -->
    <module name="UniqueProperties"/>

</module>
