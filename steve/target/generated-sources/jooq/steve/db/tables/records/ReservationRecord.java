/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.Reservation;

import org.joda.time.DateTime;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReservationRecord extends UpdatableRecordImpl<ReservationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.reservation.reservation_pk</code>.
     */
    public ReservationRecord setReservationPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.reservation_pk</code>.
     */
    public Integer getReservationPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.reservation.connector_pk</code>.
     */
    public ReservationRecord setConnectorPk(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>stevedb.reservation.transaction_pk</code>.
     */
    public ReservationRecord setTransactionPk(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.transaction_pk</code>.
     */
    public Integer getTransactionPk() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>stevedb.reservation.id_tag</code>.
     */
    public ReservationRecord setIdTag(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.id_tag</code>.
     */
    public String getIdTag() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.reservation.start_datetime</code>.
     */
    public ReservationRecord setStartDatetime(DateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.start_datetime</code>.
     */
    public DateTime getStartDatetime() {
        return (DateTime) get(4);
    }

    /**
     * Setter for <code>stevedb.reservation.expiry_datetime</code>.
     */
    public ReservationRecord setExpiryDatetime(DateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.expiry_datetime</code>.
     */
    public DateTime getExpiryDatetime() {
        return (DateTime) get(5);
    }

    /**
     * Setter for <code>stevedb.reservation.status</code>.
     */
    public ReservationRecord setStatus(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.reservation.status</code>.
     */
    public String getStatus() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ReservationRecord
     */
    public ReservationRecord() {
        super(Reservation.RESERVATION);
    }

    /**
     * Create a detached, initialised ReservationRecord
     */
    public ReservationRecord(Integer reservationPk, Integer connectorPk, Integer transactionPk, String idTag, DateTime startDatetime, DateTime expiryDatetime, String status) {
        super(Reservation.RESERVATION);

        setReservationPk(reservationPk);
        setConnectorPk(connectorPk);
        setTransactionPk(transactionPk);
        setIdTag(idTag);
        setStartDatetime(startDatetime);
        setExpiryDatetime(expiryDatetime);
        setStatus(status);
        resetChangedOnNotNull();
    }
}
