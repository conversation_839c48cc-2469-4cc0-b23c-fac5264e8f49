/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.OcppTagActivity;

import org.joda.time.DateTime;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OcppTagActivityRecord extends TableRecordImpl<OcppTagActivityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.ocpp_tag_pk</code>.
     */
    public OcppTagActivityRecord setOcppTagPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.ocpp_tag_pk</code>.
     */
    public Integer getOcppTagPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.id_tag</code>.
     */
    public OcppTagActivityRecord setIdTag(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.id_tag</code>.
     */
    public String getIdTag() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.parent_id_tag</code>.
     */
    public OcppTagActivityRecord setParentIdTag(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.parent_id_tag</code>.
     */
    public String getParentIdTag() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.expiry_date</code>.
     */
    public OcppTagActivityRecord setExpiryDate(DateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.expiry_date</code>.
     */
    public DateTime getExpiryDate() {
        return (DateTime) get(3);
    }

    /**
     * Setter for
     * <code>stevedb.ocpp_tag_activity.max_active_transaction_count</code>.
     */
    public OcppTagActivityRecord setMaxActiveTransactionCount(Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for
     * <code>stevedb.ocpp_tag_activity.max_active_transaction_count</code>.
     */
    public Integer getMaxActiveTransactionCount() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.note</code>.
     */
    public OcppTagActivityRecord setNote(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.note</code>.
     */
    public String getNote() {
        return (String) get(5);
    }

    /**
     * Setter for
     * <code>stevedb.ocpp_tag_activity.active_transaction_count</code>.
     */
    public OcppTagActivityRecord setActiveTransactionCount(Long value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for
     * <code>stevedb.ocpp_tag_activity.active_transaction_count</code>.
     */
    public Long getActiveTransactionCount() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.in_transaction</code>.
     */
    public OcppTagActivityRecord setInTransaction(Boolean value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.in_transaction</code>.
     */
    public Boolean getInTransaction() {
        return (Boolean) get(7);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag_activity.blocked</code>.
     */
    public OcppTagActivityRecord setBlocked(Boolean value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag_activity.blocked</code>.
     */
    public Boolean getBlocked() {
        return (Boolean) get(8);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OcppTagActivityRecord
     */
    public OcppTagActivityRecord() {
        super(OcppTagActivity.OCPP_TAG_ACTIVITY);
    }

    /**
     * Create a detached, initialised OcppTagActivityRecord
     */
    public OcppTagActivityRecord(Integer ocppTagPk, String idTag, String parentIdTag, DateTime expiryDate, Integer maxActiveTransactionCount, String note, Long activeTransactionCount, Boolean inTransaction, Boolean blocked) {
        super(OcppTagActivity.OCPP_TAG_ACTIVITY);

        setOcppTagPk(ocppTagPk);
        setIdTag(idTag);
        setParentIdTag(parentIdTag);
        setExpiryDate(expiryDate);
        setMaxActiveTransactionCount(maxActiveTransactionCount);
        setNote(note);
        setActiveTransactionCount(activeTransactionCount);
        setInTransaction(inTransaction);
        setBlocked(blocked);
        resetChangedOnNotNull();
    }
}
