/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.WebUser;

import org.jooq.JSON;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class WebUserRecord extends UpdatableRecordImpl<WebUserRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.web_user.web_user_pk</code>.
     */
    public WebUserRecord setWebUserPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.web_user.web_user_pk</code>.
     */
    public Integer getWebUserPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.web_user.username</code>.
     */
    public WebUserRecord setUsername(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.web_user.username</code>.
     */
    public String getUsername() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.web_user.password</code>.
     */
    public WebUserRecord setPassword(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.web_user.password</code>.
     */
    public String getPassword() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.web_user.api_password</code>.
     */
    public WebUserRecord setApiPassword(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.web_user.api_password</code>.
     */
    public String getApiPassword() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.web_user.enabled</code>.
     */
    public WebUserRecord setEnabled(Boolean value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.web_user.enabled</code>.
     */
    public Boolean getEnabled() {
        return (Boolean) get(4);
    }

    /**
     * Setter for <code>stevedb.web_user.authorities</code>.
     */
    public WebUserRecord setAuthorities(JSON value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.web_user.authorities</code>.
     */
    public JSON getAuthorities() {
        return (JSON) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached WebUserRecord
     */
    public WebUserRecord() {
        super(WebUser.WEB_USER);
    }

    /**
     * Create a detached, initialised WebUserRecord
     */
    public WebUserRecord(Integer webUserPk, String username, String password, String apiPassword, Boolean enabled, JSON authorities) {
        super(WebUser.WEB_USER);

        setWebUserPk(webUserPk);
        setUsername(username);
        setPassword(password);
        setApiPassword(apiPassword);
        setEnabled(enabled);
        setAuthorities(authorities);
        resetChangedOnNotNull();
    }
}
