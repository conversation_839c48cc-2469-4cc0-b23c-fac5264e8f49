/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.records.SettingsRecord;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Settings extends TableImpl<SettingsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.settings</code>
     */
    public static final Settings SETTINGS = new Settings();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SettingsRecord> getRecordType() {
        return SettingsRecord.class;
    }

    /**
     * The column <code>stevedb.settings.app_id</code>.
     */
    public final TableField<SettingsRecord, String> APP_ID = createField(DSL.name("app_id"), SQLDataType.VARCHAR(40).nullable(false), this, "");

    /**
     * The column <code>stevedb.settings.heartbeat_interval_in_seconds</code>.
     */
    public final TableField<SettingsRecord, Integer> HEARTBEAT_INTERVAL_IN_SECONDS = createField(DSL.name("heartbeat_interval_in_seconds"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.settings.hours_to_expire</code>.
     */
    public final TableField<SettingsRecord, Integer> HOURS_TO_EXPIRE = createField(DSL.name("hours_to_expire"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.settings.mail_enabled</code>.
     */
    public final TableField<SettingsRecord, Boolean> MAIL_ENABLED = createField(DSL.name("mail_enabled"), SQLDataType.BOOLEAN.defaultValue(DSL.inline("0", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>stevedb.settings.mail_host</code>.
     */
    public final TableField<SettingsRecord, String> MAIL_HOST = createField(DSL.name("mail_host"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.settings.mail_username</code>.
     */
    public final TableField<SettingsRecord, String> MAIL_USERNAME = createField(DSL.name("mail_username"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.settings.mail_password</code>.
     */
    public final TableField<SettingsRecord, String> MAIL_PASSWORD = createField(DSL.name("mail_password"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.settings.mail_from</code>.
     */
    public final TableField<SettingsRecord, String> MAIL_FROM = createField(DSL.name("mail_from"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.settings.mail_protocol</code>.
     */
    public final TableField<SettingsRecord, String> MAIL_PROTOCOL = createField(DSL.name("mail_protocol"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("'smtp'", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.settings.mail_port</code>.
     */
    public final TableField<SettingsRecord, Integer> MAIL_PORT = createField(DSL.name("mail_port"), SQLDataType.INTEGER.defaultValue(DSL.inline("25", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.settings.mail_recipients</code>. comma separated
     * list of email addresses
     */
    public final TableField<SettingsRecord, String> MAIL_RECIPIENTS = createField(DSL.name("mail_recipients"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "comma separated list of email addresses");

    /**
     * The column <code>stevedb.settings.notification_features</code>. comma
     * separated list
     */
    public final TableField<SettingsRecord, String> NOTIFICATION_FEATURES = createField(DSL.name("notification_features"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "comma separated list");

    private Settings(Name alias, Table<SettingsRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private Settings(Name alias, Table<SettingsRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.settings</code> table reference
     */
    public Settings(String alias) {
        this(DSL.name(alias), SETTINGS);
    }

    /**
     * Create an aliased <code>stevedb.settings</code> table reference
     */
    public Settings(Name alias) {
        this(alias, SETTINGS);
    }

    /**
     * Create a <code>stevedb.settings</code> table reference
     */
    public Settings() {
        this(DSL.name("settings"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public UniqueKey<SettingsRecord> getPrimaryKey() {
        return Keys.KEY_SETTINGS_PRIMARY;
    }

    @Override
    public List<UniqueKey<SettingsRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_SETTINGS_SETTINGS_ID_UNIQUE);
    }

    @Override
    public Settings as(String alias) {
        return new Settings(DSL.name(alias), this);
    }

    @Override
    public Settings as(Name alias) {
        return new Settings(alias, this);
    }

    @Override
    public Settings as(Table<?> alias) {
        return new Settings(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public Settings rename(String name) {
        return new Settings(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Settings rename(Name name) {
        return new Settings(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public Settings rename(Table<?> name) {
        return new Settings(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Settings where(Condition condition) {
        return new Settings(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Settings where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Settings where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Settings where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Settings where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Settings where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Settings where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Settings where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Settings whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Settings whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
