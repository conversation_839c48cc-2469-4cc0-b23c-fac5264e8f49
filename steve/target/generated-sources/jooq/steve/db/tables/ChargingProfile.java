/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.math.BigDecimal;
import java.util.Collection;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.ChargingSchedulePeriod.ChargingSchedulePeriodPath;
import jooq.steve.db.tables.Connector.ConnectorPath;
import jooq.steve.db.tables.ConnectorChargingProfile.ConnectorChargingProfilePath;
import jooq.steve.db.tables.records.ChargingProfileRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChargingProfile extends TableImpl<ChargingProfileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.charging_profile</code>
     */
    public static final ChargingProfile CHARGING_PROFILE = new ChargingProfile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ChargingProfileRecord> getRecordType() {
        return ChargingProfileRecord.class;
    }

    /**
     * The column <code>stevedb.charging_profile.charging_profile_pk</code>.
     */
    public final TableField<ChargingProfileRecord, Integer> CHARGING_PROFILE_PK = createField(DSL.name("charging_profile_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.charging_profile.stack_level</code>.
     */
    public final TableField<ChargingProfileRecord, Integer> STACK_LEVEL = createField(DSL.name("stack_level"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column
     * <code>stevedb.charging_profile.charging_profile_purpose</code>.
     */
    public final TableField<ChargingProfileRecord, String> CHARGING_PROFILE_PURPOSE = createField(DSL.name("charging_profile_purpose"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.charging_profile.charging_profile_kind</code>.
     */
    public final TableField<ChargingProfileRecord, String> CHARGING_PROFILE_KIND = createField(DSL.name("charging_profile_kind"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.charging_profile.recurrency_kind</code>.
     */
    public final TableField<ChargingProfileRecord, String> RECURRENCY_KIND = createField(DSL.name("recurrency_kind"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charging_profile.valid_from</code>.
     */
    public final TableField<ChargingProfileRecord, DateTime> VALID_FROM = createField(DSL.name("valid_from"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.charging_profile.valid_to</code>.
     */
    public final TableField<ChargingProfileRecord, DateTime> VALID_TO = createField(DSL.name("valid_to"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.charging_profile.duration_in_seconds</code>.
     */
    public final TableField<ChargingProfileRecord, Integer> DURATION_IN_SECONDS = createField(DSL.name("duration_in_seconds"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.charging_profile.start_schedule</code>.
     */
    public final TableField<ChargingProfileRecord, DateTime> START_SCHEDULE = createField(DSL.name("start_schedule"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.charging_profile.charging_rate_unit</code>.
     */
    public final TableField<ChargingProfileRecord, String> CHARGING_RATE_UNIT = createField(DSL.name("charging_rate_unit"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.charging_profile.min_charging_rate</code>.
     */
    public final TableField<ChargingProfileRecord, BigDecimal> MIN_CHARGING_RATE = createField(DSL.name("min_charging_rate"), SQLDataType.DECIMAL(15, 1).defaultValue(DSL.inline("NULL", SQLDataType.DECIMAL)), this, "");

    /**
     * The column <code>stevedb.charging_profile.description</code>.
     */
    public final TableField<ChargingProfileRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charging_profile.note</code>.
     */
    public final TableField<ChargingProfileRecord, String> NOTE = createField(DSL.name("note"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    private ChargingProfile(Name alias, Table<ChargingProfileRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ChargingProfile(Name alias, Table<ChargingProfileRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.charging_profile</code> table reference
     */
    public ChargingProfile(String alias) {
        this(DSL.name(alias), CHARGING_PROFILE);
    }

    /**
     * Create an aliased <code>stevedb.charging_profile</code> table reference
     */
    public ChargingProfile(Name alias) {
        this(alias, CHARGING_PROFILE);
    }

    /**
     * Create a <code>stevedb.charging_profile</code> table reference
     */
    public ChargingProfile() {
        this(DSL.name("charging_profile"), null);
    }

    public <O extends Record> ChargingProfile(Table<O> path, ForeignKey<O, ChargingProfileRecord> childPath, InverseForeignKey<O, ChargingProfileRecord> parentPath) {
        super(path, childPath, parentPath, CHARGING_PROFILE);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ChargingProfilePath extends ChargingProfile implements Path<ChargingProfileRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ChargingProfilePath(Table<O> path, ForeignKey<O, ChargingProfileRecord> childPath, InverseForeignKey<O, ChargingProfileRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ChargingProfilePath(Name alias, Table<ChargingProfileRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ChargingProfilePath as(String alias) {
            return new ChargingProfilePath(DSL.name(alias), this);
        }

        @Override
        public ChargingProfilePath as(Name alias) {
            return new ChargingProfilePath(alias, this);
        }

        @Override
        public ChargingProfilePath as(Table<?> alias) {
            return new ChargingProfilePath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public Identity<ChargingProfileRecord, Integer> getIdentity() {
        return (Identity<ChargingProfileRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<ChargingProfileRecord> getPrimaryKey() {
        return Keys.KEY_CHARGING_PROFILE_PRIMARY;
    }

    private transient ChargingSchedulePeriodPath _chargingSchedulePeriod;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.charging_schedule_period</code> table
     */
    public ChargingSchedulePeriodPath chargingSchedulePeriod() {
        if (_chargingSchedulePeriod == null)
            _chargingSchedulePeriod = new ChargingSchedulePeriodPath(this, null, Keys.FK_CHARGING_SCHEDULE_PERIOD_CHARGING_PROFILE_PK.getInverseKey());

        return _chargingSchedulePeriod;
    }

    private transient ConnectorChargingProfilePath _connectorChargingProfile;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.connector_charging_profile</code> table
     */
    public ConnectorChargingProfilePath connectorChargingProfile() {
        if (_connectorChargingProfile == null)
            _connectorChargingProfile = new ConnectorChargingProfilePath(this, null, Keys.FK_CONNECTOR_CHARGING_PROFILE_CHARGING_PROFILE_PK.getInverseKey());

        return _connectorChargingProfile;
    }

    /**
     * Get the implicit many-to-many join path to the
     * <code>stevedb.connector</code> table
     */
    public ConnectorPath connector() {
        return connectorChargingProfile().connector();
    }

    @Override
    public ChargingProfile as(String alias) {
        return new ChargingProfile(DSL.name(alias), this);
    }

    @Override
    public ChargingProfile as(Name alias) {
        return new ChargingProfile(alias, this);
    }

    @Override
    public ChargingProfile as(Table<?> alias) {
        return new ChargingProfile(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargingProfile rename(String name) {
        return new ChargingProfile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargingProfile rename(Name name) {
        return new ChargingProfile(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargingProfile rename(Table<?> name) {
        return new ChargingProfile(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingProfile where(Condition condition) {
        return new ChargingProfile(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingProfile where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingProfile where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingProfile where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingProfile where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingProfile where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingProfile where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingProfile where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingProfile whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingProfile whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
