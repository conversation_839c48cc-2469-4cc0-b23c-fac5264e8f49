/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.ConnectorChargingProfile;

import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorChargingProfileRecord extends TableRecordImpl<ConnectorChargingProfileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.connector_charging_profile.connector_pk</code>.
     */
    public ConnectorChargingProfileRecord setConnectorPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_charging_profile.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for
     * <code>stevedb.connector_charging_profile.charging_profile_pk</code>.
     */
    public ConnectorChargingProfileRecord setChargingProfilePk(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for
     * <code>stevedb.connector_charging_profile.charging_profile_pk</code>.
     */
    public Integer getChargingProfilePk() {
        return (Integer) get(1);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConnectorChargingProfileRecord
     */
    public ConnectorChargingProfileRecord() {
        super(ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE);
    }

    /**
     * Create a detached, initialised ConnectorChargingProfileRecord
     */
    public ConnectorChargingProfileRecord(Integer connectorPk, Integer chargingProfilePk) {
        super(ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE);

        setConnectorPk(connectorPk);
        setChargingProfilePk(chargingProfilePk);
        resetChangedOnNotNull();
    }
}
