/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Collection;

import jooq.steve.db.Stevedb;
import jooq.steve.db.enums.TransactionStopFailedEventActor;
import jooq.steve.db.tables.records.TransactionStopFailedRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionStopFailed extends TableImpl<TransactionStopFailedRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.transaction_stop_failed</code>
     */
    public static final TransactionStopFailed TRANSACTION_STOP_FAILED = new TransactionStopFailed();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TransactionStopFailedRecord> getRecordType() {
        return TransactionStopFailedRecord.class;
    }

    /**
     * The column <code>stevedb.transaction_stop_failed.transaction_pk</code>.
     */
    public final TableField<TransactionStopFailedRecord, Integer> TRANSACTION_PK = createField(DSL.name("transaction_pk"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.transaction_stop_failed.charge_box_id</code>.
     */
    public final TableField<TransactionStopFailedRecord, String> CHARGE_BOX_ID = createField(DSL.name("charge_box_id"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.transaction_stop_failed.event_timestamp</code>.
     */
    public final TableField<TransactionStopFailedRecord, DateTime> EVENT_TIMESTAMP = createField(DSL.name("event_timestamp"), SQLDataType.TIMESTAMP(6).nullable(false).defaultValue(DSL.field(DSL.raw("current_timestamp(6)"), SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.transaction_stop_failed.event_actor</code>.
     */
    public final TableField<TransactionStopFailedRecord, TransactionStopFailedEventActor> EVENT_ACTOR = createField(DSL.name("event_actor"), SQLDataType.VARCHAR(7).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)).asEnumDataType(TransactionStopFailedEventActor.class), this, "");

    /**
     * The column <code>stevedb.transaction_stop_failed.stop_timestamp</code>.
     */
    public final TableField<TransactionStopFailedRecord, DateTime> STOP_TIMESTAMP = createField(DSL.name("stop_timestamp"), SQLDataType.TIMESTAMP(6).nullable(false).defaultValue(DSL.field(DSL.raw("current_timestamp(6)"), SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.transaction_stop_failed.stop_value</code>.
     */
    public final TableField<TransactionStopFailedRecord, String> STOP_VALUE = createField(DSL.name("stop_value"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.transaction_stop_failed.stop_reason</code>.
     */
    public final TableField<TransactionStopFailedRecord, String> STOP_REASON = createField(DSL.name("stop_reason"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.transaction_stop_failed.fail_reason</code>.
     */
    public final TableField<TransactionStopFailedRecord, String> FAIL_REASON = createField(DSL.name("fail_reason"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    private TransactionStopFailed(Name alias, Table<TransactionStopFailedRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private TransactionStopFailed(Name alias, Table<TransactionStopFailedRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.transaction_stop_failed</code> table
     * reference
     */
    public TransactionStopFailed(String alias) {
        this(DSL.name(alias), TRANSACTION_STOP_FAILED);
    }

    /**
     * Create an aliased <code>stevedb.transaction_stop_failed</code> table
     * reference
     */
    public TransactionStopFailed(Name alias) {
        this(alias, TRANSACTION_STOP_FAILED);
    }

    /**
     * Create a <code>stevedb.transaction_stop_failed</code> table reference
     */
    public TransactionStopFailed() {
        this(DSL.name("transaction_stop_failed"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public TransactionStopFailed as(String alias) {
        return new TransactionStopFailed(DSL.name(alias), this);
    }

    @Override
    public TransactionStopFailed as(Name alias) {
        return new TransactionStopFailed(alias, this);
    }

    @Override
    public TransactionStopFailed as(Table<?> alias) {
        return new TransactionStopFailed(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStopFailed rename(String name) {
        return new TransactionStopFailed(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStopFailed rename(Name name) {
        return new TransactionStopFailed(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStopFailed rename(Table<?> name) {
        return new TransactionStopFailed(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStopFailed where(Condition condition) {
        return new TransactionStopFailed(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStopFailed where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStopFailed where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStopFailed where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStopFailed where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStopFailed where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStopFailed where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStopFailed where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStopFailed whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStopFailed whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
