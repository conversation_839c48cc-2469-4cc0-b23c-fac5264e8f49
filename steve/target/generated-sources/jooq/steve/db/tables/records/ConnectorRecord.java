/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.Connector;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorRecord extends UpdatableRecordImpl<ConnectorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.connector.connector_pk</code>.
     */
    public ConnectorRecord setConnectorPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.connector.charge_box_id</code>.
     */
    public ConnectorRecord setChargeBoxId(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector.charge_box_id</code>.
     */
    public String getChargeBoxId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.connector.connector_id</code>.
     */
    public ConnectorRecord setConnectorId(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector.connector_id</code>.
     */
    public Integer getConnectorId() {
        return (Integer) get(2);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConnectorRecord
     */
    public ConnectorRecord() {
        super(Connector.CONNECTOR);
    }

    /**
     * Create a detached, initialised ConnectorRecord
     */
    public ConnectorRecord(Integer connectorPk, String chargeBoxId, Integer connectorId) {
        super(Connector.CONNECTOR);

        setConnectorPk(connectorPk);
        setChargeBoxId(chargeBoxId);
        setConnectorId(connectorId);
        resetChangedOnNotNull();
    }
}
