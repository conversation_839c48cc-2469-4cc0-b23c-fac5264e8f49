/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.Settings;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettingsRecord extends UpdatableRecordImpl<SettingsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.settings.app_id</code>.
     */
    public SettingsRecord setAppId(String value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.app_id</code>.
     */
    public String getAppId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>stevedb.settings.heartbeat_interval_in_seconds</code>.
     */
    public SettingsRecord setHeartbeatIntervalInSeconds(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.heartbeat_interval_in_seconds</code>.
     */
    public Integer getHeartbeatIntervalInSeconds() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>stevedb.settings.hours_to_expire</code>.
     */
    public SettingsRecord setHoursToExpire(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.hours_to_expire</code>.
     */
    public Integer getHoursToExpire() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>stevedb.settings.mail_enabled</code>.
     */
    public SettingsRecord setMailEnabled(Boolean value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_enabled</code>.
     */
    public Boolean getMailEnabled() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>stevedb.settings.mail_host</code>.
     */
    public SettingsRecord setMailHost(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_host</code>.
     */
    public String getMailHost() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.settings.mail_username</code>.
     */
    public SettingsRecord setMailUsername(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_username</code>.
     */
    public String getMailUsername() {
        return (String) get(5);
    }

    /**
     * Setter for <code>stevedb.settings.mail_password</code>.
     */
    public SettingsRecord setMailPassword(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_password</code>.
     */
    public String getMailPassword() {
        return (String) get(6);
    }

    /**
     * Setter for <code>stevedb.settings.mail_from</code>.
     */
    public SettingsRecord setMailFrom(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_from</code>.
     */
    public String getMailFrom() {
        return (String) get(7);
    }

    /**
     * Setter for <code>stevedb.settings.mail_protocol</code>.
     */
    public SettingsRecord setMailProtocol(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_protocol</code>.
     */
    public String getMailProtocol() {
        return (String) get(8);
    }

    /**
     * Setter for <code>stevedb.settings.mail_port</code>.
     */
    public SettingsRecord setMailPort(Integer value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_port</code>.
     */
    public Integer getMailPort() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>stevedb.settings.mail_recipients</code>. comma separated
     * list of email addresses
     */
    public SettingsRecord setMailRecipients(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.mail_recipients</code>. comma separated
     * list of email addresses
     */
    public String getMailRecipients() {
        return (String) get(10);
    }

    /**
     * Setter for <code>stevedb.settings.notification_features</code>. comma
     * separated list
     */
    public SettingsRecord setNotificationFeatures(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.settings.notification_features</code>. comma
     * separated list
     */
    public String getNotificationFeatures() {
        return (String) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SettingsRecord
     */
    public SettingsRecord() {
        super(Settings.SETTINGS);
    }

    /**
     * Create a detached, initialised SettingsRecord
     */
    public SettingsRecord(String appId, Integer heartbeatIntervalInSeconds, Integer hoursToExpire, Boolean mailEnabled, String mailHost, String mailUsername, String mailPassword, String mailFrom, String mailProtocol, Integer mailPort, String mailRecipients, String notificationFeatures) {
        super(Settings.SETTINGS);

        setAppId(appId);
        setHeartbeatIntervalInSeconds(heartbeatIntervalInSeconds);
        setHoursToExpire(hoursToExpire);
        setMailEnabled(mailEnabled);
        setMailHost(mailHost);
        setMailUsername(mailUsername);
        setMailPassword(mailPassword);
        setMailFrom(mailFrom);
        setMailProtocol(mailProtocol);
        setMailPort(mailPort);
        setMailRecipients(mailRecipients);
        setNotificationFeatures(notificationFeatures);
        resetChangedOnNotNull();
    }
}
