/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import java.math.BigDecimal;

import jooq.steve.db.tables.ChargingProfile;

import org.joda.time.DateTime;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChargingProfileRecord extends UpdatableRecordImpl<ChargingProfileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.charging_profile.charging_profile_pk</code>.
     */
    public ChargingProfileRecord setChargingProfilePk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.charging_profile_pk</code>.
     */
    public Integer getChargingProfilePk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.charging_profile.stack_level</code>.
     */
    public ChargingProfileRecord setStackLevel(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.stack_level</code>.
     */
    public Integer getStackLevel() {
        return (Integer) get(1);
    }

    /**
     * Setter for
     * <code>stevedb.charging_profile.charging_profile_purpose</code>.
     */
    public ChargingProfileRecord setChargingProfilePurpose(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for
     * <code>stevedb.charging_profile.charging_profile_purpose</code>.
     */
    public String getChargingProfilePurpose() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.charging_profile.charging_profile_kind</code>.
     */
    public ChargingProfileRecord setChargingProfileKind(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.charging_profile_kind</code>.
     */
    public String getChargingProfileKind() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.charging_profile.recurrency_kind</code>.
     */
    public ChargingProfileRecord setRecurrencyKind(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.recurrency_kind</code>.
     */
    public String getRecurrencyKind() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.charging_profile.valid_from</code>.
     */
    public ChargingProfileRecord setValidFrom(DateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.valid_from</code>.
     */
    public DateTime getValidFrom() {
        return (DateTime) get(5);
    }

    /**
     * Setter for <code>stevedb.charging_profile.valid_to</code>.
     */
    public ChargingProfileRecord setValidTo(DateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.valid_to</code>.
     */
    public DateTime getValidTo() {
        return (DateTime) get(6);
    }

    /**
     * Setter for <code>stevedb.charging_profile.duration_in_seconds</code>.
     */
    public ChargingProfileRecord setDurationInSeconds(Integer value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.duration_in_seconds</code>.
     */
    public Integer getDurationInSeconds() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>stevedb.charging_profile.start_schedule</code>.
     */
    public ChargingProfileRecord setStartSchedule(DateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.start_schedule</code>.
     */
    public DateTime getStartSchedule() {
        return (DateTime) get(8);
    }

    /**
     * Setter for <code>stevedb.charging_profile.charging_rate_unit</code>.
     */
    public ChargingProfileRecord setChargingRateUnit(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.charging_rate_unit</code>.
     */
    public String getChargingRateUnit() {
        return (String) get(9);
    }

    /**
     * Setter for <code>stevedb.charging_profile.min_charging_rate</code>.
     */
    public ChargingProfileRecord setMinChargingRate(BigDecimal value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.min_charging_rate</code>.
     */
    public BigDecimal getMinChargingRate() {
        return (BigDecimal) get(10);
    }

    /**
     * Setter for <code>stevedb.charging_profile.description</code>.
     */
    public ChargingProfileRecord setDescription(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.description</code>.
     */
    public String getDescription() {
        return (String) get(11);
    }

    /**
     * Setter for <code>stevedb.charging_profile.note</code>.
     */
    public ChargingProfileRecord setNote(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_profile.note</code>.
     */
    public String getNote() {
        return (String) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ChargingProfileRecord
     */
    public ChargingProfileRecord() {
        super(ChargingProfile.CHARGING_PROFILE);
    }

    /**
     * Create a detached, initialised ChargingProfileRecord
     */
    public ChargingProfileRecord(Integer chargingProfilePk, Integer stackLevel, String chargingProfilePurpose, String chargingProfileKind, String recurrencyKind, DateTime validFrom, DateTime validTo, Integer durationInSeconds, DateTime startSchedule, String chargingRateUnit, BigDecimal minChargingRate, String description, String note) {
        super(ChargingProfile.CHARGING_PROFILE);

        setChargingProfilePk(chargingProfilePk);
        setStackLevel(stackLevel);
        setChargingProfilePurpose(chargingProfilePurpose);
        setChargingProfileKind(chargingProfileKind);
        setRecurrencyKind(recurrencyKind);
        setValidFrom(validFrom);
        setValidTo(validTo);
        setDurationInSeconds(durationInSeconds);
        setStartSchedule(startSchedule);
        setChargingRateUnit(chargingRateUnit);
        setMinChargingRate(minChargingRate);
        setDescription(description);
        setNote(note);
        resetChangedOnNotNull();
    }
}
