/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Indexes;
import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.Connector.ConnectorPath;
import jooq.steve.db.tables.TransactionStart.TransactionStartPath;
import jooq.steve.db.tables.records.ConnectorMeterValueRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorMeterValue extends TableImpl<ConnectorMeterValueRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.connector_meter_value</code>
     */
    public static final ConnectorMeterValue CONNECTOR_METER_VALUE = new ConnectorMeterValue();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConnectorMeterValueRecord> getRecordType() {
        return ConnectorMeterValueRecord.class;
    }

    /**
     * The column <code>stevedb.connector_meter_value.connector_pk</code>.
     */
    public final TableField<ConnectorMeterValueRecord, Integer> CONNECTOR_PK = createField(DSL.name("connector_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.transaction_pk</code>.
     */
    public final TableField<ConnectorMeterValueRecord, Integer> TRANSACTION_PK = createField(DSL.name("transaction_pk"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.value_timestamp</code>.
     */
    public final TableField<ConnectorMeterValueRecord, DateTime> VALUE_TIMESTAMP = createField(DSL.name("value_timestamp"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.connector_meter_value.value</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> VALUE = createField(DSL.name("value"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.reading_context</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> READING_CONTEXT = createField(DSL.name("reading_context"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.format</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> FORMAT = createField(DSL.name("format"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.measurand</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> MEASURAND = createField(DSL.name("measurand"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.location</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> LOCATION = createField(DSL.name("location"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.unit</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> UNIT = createField(DSL.name("unit"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_meter_value.phase</code>.
     */
    public final TableField<ConnectorMeterValueRecord, String> PHASE = createField(DSL.name("phase"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    private ConnectorMeterValue(Name alias, Table<ConnectorMeterValueRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ConnectorMeterValue(Name alias, Table<ConnectorMeterValueRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.connector_meter_value</code> table
     * reference
     */
    public ConnectorMeterValue(String alias) {
        this(DSL.name(alias), CONNECTOR_METER_VALUE);
    }

    /**
     * Create an aliased <code>stevedb.connector_meter_value</code> table
     * reference
     */
    public ConnectorMeterValue(Name alias) {
        this(alias, CONNECTOR_METER_VALUE);
    }

    /**
     * Create a <code>stevedb.connector_meter_value</code> table reference
     */
    public ConnectorMeterValue() {
        this(DSL.name("connector_meter_value"), null);
    }

    public <O extends Record> ConnectorMeterValue(Table<O> path, ForeignKey<O, ConnectorMeterValueRecord> childPath, InverseForeignKey<O, ConnectorMeterValueRecord> parentPath) {
        super(path, childPath, parentPath, CONNECTOR_METER_VALUE);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ConnectorMeterValuePath extends ConnectorMeterValue implements Path<ConnectorMeterValueRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ConnectorMeterValuePath(Table<O> path, ForeignKey<O, ConnectorMeterValueRecord> childPath, InverseForeignKey<O, ConnectorMeterValueRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ConnectorMeterValuePath(Name alias, Table<ConnectorMeterValueRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ConnectorMeterValuePath as(String alias) {
            return new ConnectorMeterValuePath(DSL.name(alias), this);
        }

        @Override
        public ConnectorMeterValuePath as(Name alias) {
            return new ConnectorMeterValuePath(alias, this);
        }

        @Override
        public ConnectorMeterValuePath as(Table<?> alias) {
            return new ConnectorMeterValuePath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.CONNECTOR_METER_VALUE_CMV_VALUE_TIMESTAMP_IDX, Indexes.CONNECTOR_METER_VALUE_FK_CM_PK_IDX, Indexes.CONNECTOR_METER_VALUE_FK_TID_CM_IDX);
    }

    @Override
    public List<ForeignKey<ConnectorMeterValueRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_PK_CM, Keys.FK_TID_CM);
    }

    private transient ConnectorPath _connector;

    /**
     * Get the implicit join path to the <code>stevedb.connector</code> table.
     */
    public ConnectorPath connector() {
        if (_connector == null)
            _connector = new ConnectorPath(this, Keys.FK_PK_CM, null);

        return _connector;
    }

    private transient TransactionStartPath _transactionStart;

    /**
     * Get the implicit join path to the <code>stevedb.transaction_start</code>
     * table.
     */
    public TransactionStartPath transactionStart() {
        if (_transactionStart == null)
            _transactionStart = new TransactionStartPath(this, Keys.FK_TID_CM, null);

        return _transactionStart;
    }

    @Override
    public ConnectorMeterValue as(String alias) {
        return new ConnectorMeterValue(DSL.name(alias), this);
    }

    @Override
    public ConnectorMeterValue as(Name alias) {
        return new ConnectorMeterValue(alias, this);
    }

    @Override
    public ConnectorMeterValue as(Table<?> alias) {
        return new ConnectorMeterValue(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorMeterValue rename(String name) {
        return new ConnectorMeterValue(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorMeterValue rename(Name name) {
        return new ConnectorMeterValue(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorMeterValue rename(Table<?> name) {
        return new ConnectorMeterValue(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorMeterValue where(Condition condition) {
        return new ConnectorMeterValue(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorMeterValue where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorMeterValue where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorMeterValue where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorMeterValue where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorMeterValue where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorMeterValue where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorMeterValue where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorMeterValue whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorMeterValue whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
