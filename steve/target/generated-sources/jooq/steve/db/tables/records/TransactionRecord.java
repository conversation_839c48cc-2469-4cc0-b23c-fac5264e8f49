/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.enums.TransactionStopEventActor;
import jooq.steve.db.tables.Transaction;

import org.joda.time.DateTime;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionRecord extends TableRecordImpl<TransactionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.transaction.transaction_pk</code>.
     */
    public TransactionRecord setTransactionPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.transaction_pk</code>.
     */
    public Integer getTransactionPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.transaction.connector_pk</code>.
     */
    public TransactionRecord setConnectorPk(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>stevedb.transaction.id_tag</code>.
     */
    public TransactionRecord setIdTag(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.id_tag</code>.
     */
    public String getIdTag() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.transaction.start_event_timestamp</code>.
     */
    public TransactionRecord setStartEventTimestamp(DateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.start_event_timestamp</code>.
     */
    public DateTime getStartEventTimestamp() {
        return (DateTime) get(3);
    }

    /**
     * Setter for <code>stevedb.transaction.start_timestamp</code>.
     */
    public TransactionRecord setStartTimestamp(DateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.start_timestamp</code>.
     */
    public DateTime getStartTimestamp() {
        return (DateTime) get(4);
    }

    /**
     * Setter for <code>stevedb.transaction.start_value</code>.
     */
    public TransactionRecord setStartValue(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.start_value</code>.
     */
    public String getStartValue() {
        return (String) get(5);
    }

    /**
     * Setter for <code>stevedb.transaction.stop_event_actor</code>.
     */
    public TransactionRecord setStopEventActor(TransactionStopEventActor value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.stop_event_actor</code>.
     */
    public TransactionStopEventActor getStopEventActor() {
        return (TransactionStopEventActor) get(6);
    }

    /**
     * Setter for <code>stevedb.transaction.stop_event_timestamp</code>.
     */
    public TransactionRecord setStopEventTimestamp(DateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.stop_event_timestamp</code>.
     */
    public DateTime getStopEventTimestamp() {
        return (DateTime) get(7);
    }

    /**
     * Setter for <code>stevedb.transaction.stop_timestamp</code>.
     */
    public TransactionRecord setStopTimestamp(DateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.stop_timestamp</code>.
     */
    public DateTime getStopTimestamp() {
        return (DateTime) get(8);
    }

    /**
     * Setter for <code>stevedb.transaction.stop_value</code>.
     */
    public TransactionRecord setStopValue(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.stop_value</code>.
     */
    public String getStopValue() {
        return (String) get(9);
    }

    /**
     * Setter for <code>stevedb.transaction.stop_reason</code>.
     */
    public TransactionRecord setStopReason(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction.stop_reason</code>.
     */
    public String getStopReason() {
        return (String) get(10);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TransactionRecord
     */
    public TransactionRecord() {
        super(Transaction.TRANSACTION);
    }

    /**
     * Create a detached, initialised TransactionRecord
     */
    public TransactionRecord(Integer transactionPk, Integer connectorPk, String idTag, DateTime startEventTimestamp, DateTime startTimestamp, String startValue, TransactionStopEventActor stopEventActor, DateTime stopEventTimestamp, DateTime stopTimestamp, String stopValue, String stopReason) {
        super(Transaction.TRANSACTION);

        setTransactionPk(transactionPk);
        setConnectorPk(connectorPk);
        setIdTag(idTag);
        setStartEventTimestamp(startEventTimestamp);
        setStartTimestamp(startTimestamp);
        setStartValue(startValue);
        setStopEventActor(stopEventActor);
        setStopEventTimestamp(stopEventTimestamp);
        setStopTimestamp(stopTimestamp);
        setStopValue(stopValue);
        setStopReason(stopReason);
        resetChangedOnNotNull();
    }
}
