/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.TransactionStart;

import org.joda.time.DateTime;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionStartRecord extends UpdatableRecordImpl<TransactionStartRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.transaction_start.transaction_pk</code>.
     */
    public TransactionStartRecord setTransactionPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_start.transaction_pk</code>.
     */
    public Integer getTransactionPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.transaction_start.event_timestamp</code>.
     */
    public TransactionStartRecord setEventTimestamp(DateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_start.event_timestamp</code>.
     */
    public DateTime getEventTimestamp() {
        return (DateTime) get(1);
    }

    /**
     * Setter for <code>stevedb.transaction_start.connector_pk</code>.
     */
    public TransactionStartRecord setConnectorPk(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_start.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>stevedb.transaction_start.id_tag</code>.
     */
    public TransactionStartRecord setIdTag(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_start.id_tag</code>.
     */
    public String getIdTag() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.transaction_start.start_timestamp</code>.
     */
    public TransactionStartRecord setStartTimestamp(DateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_start.start_timestamp</code>.
     */
    public DateTime getStartTimestamp() {
        return (DateTime) get(4);
    }

    /**
     * Setter for <code>stevedb.transaction_start.start_value</code>.
     */
    public TransactionStartRecord setStartValue(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_start.start_value</code>.
     */
    public String getStartValue() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TransactionStartRecord
     */
    public TransactionStartRecord() {
        super(TransactionStart.TRANSACTION_START);
    }

    /**
     * Create a detached, initialised TransactionStartRecord
     */
    public TransactionStartRecord(Integer transactionPk, DateTime eventTimestamp, Integer connectorPk, String idTag, DateTime startTimestamp, String startValue) {
        super(TransactionStart.TRANSACTION_START);

        setTransactionPk(transactionPk);
        setEventTimestamp(eventTimestamp);
        setConnectorPk(connectorPk);
        setIdTag(idTag);
        setStartTimestamp(startTimestamp);
        setStartValue(startValue);
        resetChangedOnNotNull();
    }
}
