/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db;


import java.util.Arrays;
import java.util.List;

import jooq.steve.db.tables.Address;
import jooq.steve.db.tables.ChargeBox;
import jooq.steve.db.tables.ChargingProfile;
import jooq.steve.db.tables.ChargingSchedulePeriod;
import jooq.steve.db.tables.Connector;
import jooq.steve.db.tables.ConnectorChargingProfile;
import jooq.steve.db.tables.ConnectorMeterValue;
import jooq.steve.db.tables.ConnectorStatus;
import jooq.steve.db.tables.OcppTag;
import jooq.steve.db.tables.OcppTagActivity;
import jooq.steve.db.tables.Reservation;
import jooq.steve.db.tables.SchemaVersion;
import jooq.steve.db.tables.Settings;
import jooq.steve.db.tables.Transaction;
import jooq.steve.db.tables.TransactionStart;
import jooq.steve.db.tables.TransactionStop;
import jooq.steve.db.tables.TransactionStopFailed;
import jooq.steve.db.tables.User;
import jooq.steve.db.tables.WebUser;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Stevedb extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb</code>
     */
    public static final Stevedb STEVEDB = new Stevedb();

    /**
     * The table <code>stevedb.address</code>.
     */
    public final Address ADDRESS = Address.ADDRESS;

    /**
     * The table <code>stevedb.charge_box</code>.
     */
    public final ChargeBox CHARGE_BOX = ChargeBox.CHARGE_BOX;

    /**
     * The table <code>stevedb.charging_profile</code>.
     */
    public final ChargingProfile CHARGING_PROFILE = ChargingProfile.CHARGING_PROFILE;

    /**
     * The table <code>stevedb.charging_schedule_period</code>.
     */
    public final ChargingSchedulePeriod CHARGING_SCHEDULE_PERIOD = ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD;

    /**
     * The table <code>stevedb.connector</code>.
     */
    public final Connector CONNECTOR = Connector.CONNECTOR;

    /**
     * The table <code>stevedb.connector_charging_profile</code>.
     */
    public final ConnectorChargingProfile CONNECTOR_CHARGING_PROFILE = ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE;

    /**
     * The table <code>stevedb.connector_meter_value</code>.
     */
    public final ConnectorMeterValue CONNECTOR_METER_VALUE = ConnectorMeterValue.CONNECTOR_METER_VALUE;

    /**
     * The table <code>stevedb.connector_status</code>.
     */
    public final ConnectorStatus CONNECTOR_STATUS = ConnectorStatus.CONNECTOR_STATUS;

    /**
     * The table <code>stevedb.ocpp_tag</code>.
     */
    public final OcppTag OCPP_TAG = OcppTag.OCPP_TAG;

    /**
     * The table <code>stevedb.ocpp_tag_activity</code>.
     */
    public final OcppTagActivity OCPP_TAG_ACTIVITY = OcppTagActivity.OCPP_TAG_ACTIVITY;

    /**
     * The table <code>stevedb.reservation</code>.
     */
    public final Reservation RESERVATION = Reservation.RESERVATION;

    /**
     * The table <code>stevedb.schema_version</code>.
     */
    public final SchemaVersion SCHEMA_VERSION = SchemaVersion.SCHEMA_VERSION;

    /**
     * The table <code>stevedb.settings</code>.
     */
    public final Settings SETTINGS = Settings.SETTINGS;

    /**
     * The table <code>stevedb.transaction</code>.
     */
    public final Transaction TRANSACTION = Transaction.TRANSACTION;

    /**
     * The table <code>stevedb.transaction_start</code>.
     */
    public final TransactionStart TRANSACTION_START = TransactionStart.TRANSACTION_START;

    /**
     * The table <code>stevedb.transaction_stop</code>.
     */
    public final TransactionStop TRANSACTION_STOP = TransactionStop.TRANSACTION_STOP;

    /**
     * The table <code>stevedb.transaction_stop_failed</code>.
     */
    public final TransactionStopFailed TRANSACTION_STOP_FAILED = TransactionStopFailed.TRANSACTION_STOP_FAILED;

    /**
     * The table <code>stevedb.user</code>.
     */
    public final User USER = User.USER;

    /**
     * The table <code>stevedb.web_user</code>.
     */
    public final WebUser WEB_USER = WebUser.WEB_USER;

    /**
     * No further instances allowed
     */
    private Stevedb() {
        super("stevedb", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.asList(
            Address.ADDRESS,
            ChargeBox.CHARGE_BOX,
            ChargingProfile.CHARGING_PROFILE,
            ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD,
            Connector.CONNECTOR,
            ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE,
            ConnectorMeterValue.CONNECTOR_METER_VALUE,
            ConnectorStatus.CONNECTOR_STATUS,
            OcppTag.OCPP_TAG,
            OcppTagActivity.OCPP_TAG_ACTIVITY,
            Reservation.RESERVATION,
            SchemaVersion.SCHEMA_VERSION,
            Settings.SETTINGS,
            Transaction.TRANSACTION,
            TransactionStart.TRANSACTION_START,
            TransactionStop.TRANSACTION_STOP,
            TransactionStopFailed.TRANSACTION_STOP_FAILED,
            User.USER,
            WebUser.WEB_USER
        );
    }
}
