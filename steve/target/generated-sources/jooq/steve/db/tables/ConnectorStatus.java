/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Indexes;
import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.Connector.ConnectorPath;
import jooq.steve.db.tables.records.ConnectorStatusRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorStatus extends TableImpl<ConnectorStatusRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.connector_status</code>
     */
    public static final ConnectorStatus CONNECTOR_STATUS = new ConnectorStatus();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConnectorStatusRecord> getRecordType() {
        return ConnectorStatusRecord.class;
    }

    /**
     * The column <code>stevedb.connector_status.connector_pk</code>.
     */
    public final TableField<ConnectorStatusRecord, Integer> CONNECTOR_PK = createField(DSL.name("connector_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column <code>stevedb.connector_status.status_timestamp</code>.
     */
    public final TableField<ConnectorStatusRecord, DateTime> STATUS_TIMESTAMP = createField(DSL.name("status_timestamp"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.connector_status.status</code>.
     */
    public final TableField<ConnectorStatusRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_status.error_code</code>.
     */
    public final TableField<ConnectorStatusRecord, String> ERROR_CODE = createField(DSL.name("error_code"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_status.error_info</code>.
     */
    public final TableField<ConnectorStatusRecord, String> ERROR_INFO = createField(DSL.name("error_info"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_status.vendor_id</code>.
     */
    public final TableField<ConnectorStatusRecord, String> VENDOR_ID = createField(DSL.name("vendor_id"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.connector_status.vendor_error_code</code>.
     */
    public final TableField<ConnectorStatusRecord, String> VENDOR_ERROR_CODE = createField(DSL.name("vendor_error_code"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    private ConnectorStatus(Name alias, Table<ConnectorStatusRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ConnectorStatus(Name alias, Table<ConnectorStatusRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.connector_status</code> table reference
     */
    public ConnectorStatus(String alias) {
        this(DSL.name(alias), CONNECTOR_STATUS);
    }

    /**
     * Create an aliased <code>stevedb.connector_status</code> table reference
     */
    public ConnectorStatus(Name alias) {
        this(alias, CONNECTOR_STATUS);
    }

    /**
     * Create a <code>stevedb.connector_status</code> table reference
     */
    public ConnectorStatus() {
        this(DSL.name("connector_status"), null);
    }

    public <O extends Record> ConnectorStatus(Table<O> path, ForeignKey<O, ConnectorStatusRecord> childPath, InverseForeignKey<O, ConnectorStatusRecord> parentPath) {
        super(path, childPath, parentPath, CONNECTOR_STATUS);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ConnectorStatusPath extends ConnectorStatus implements Path<ConnectorStatusRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ConnectorStatusPath(Table<O> path, ForeignKey<O, ConnectorStatusRecord> childPath, InverseForeignKey<O, ConnectorStatusRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ConnectorStatusPath(Name alias, Table<ConnectorStatusRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ConnectorStatusPath as(String alias) {
            return new ConnectorStatusPath(DSL.name(alias), this);
        }

        @Override
        public ConnectorStatusPath as(Name alias) {
            return new ConnectorStatusPath(alias, this);
        }

        @Override
        public ConnectorStatusPath as(Table<?> alias) {
            return new ConnectorStatusPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.CONNECTOR_STATUS_CONNECTOR_STATUS_CPK_ST_IDX, Indexes.CONNECTOR_STATUS_FK_CS_PK_IDX);
    }

    @Override
    public List<ForeignKey<ConnectorStatusRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_CS_PK);
    }

    private transient ConnectorPath _connector;

    /**
     * Get the implicit join path to the <code>stevedb.connector</code> table.
     */
    public ConnectorPath connector() {
        if (_connector == null)
            _connector = new ConnectorPath(this, Keys.FK_CS_PK, null);

        return _connector;
    }

    @Override
    public ConnectorStatus as(String alias) {
        return new ConnectorStatus(DSL.name(alias), this);
    }

    @Override
    public ConnectorStatus as(Name alias) {
        return new ConnectorStatus(alias, this);
    }

    @Override
    public ConnectorStatus as(Table<?> alias) {
        return new ConnectorStatus(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorStatus rename(String name) {
        return new ConnectorStatus(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorStatus rename(Name name) {
        return new ConnectorStatus(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorStatus rename(Table<?> name) {
        return new ConnectorStatus(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorStatus where(Condition condition) {
        return new ConnectorStatus(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorStatus where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorStatus where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorStatus where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorStatus where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorStatus where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorStatus where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorStatus where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorStatus whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorStatus whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
