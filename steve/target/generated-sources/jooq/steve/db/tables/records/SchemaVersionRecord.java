/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.SchemaVersion;

import org.joda.time.DateTime;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SchemaVersionRecord extends UpdatableRecordImpl<SchemaVersionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.schema_version.installed_rank</code>.
     */
    public SchemaVersionRecord setInstalledRank(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.installed_rank</code>.
     */
    public Integer getInstalledRank() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.schema_version.version</code>.
     */
    public SchemaVersionRecord setVersion(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.version</code>.
     */
    public String getVersion() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.schema_version.description</code>.
     */
    public SchemaVersionRecord setDescription(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.description</code>.
     */
    public String getDescription() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.schema_version.type</code>.
     */
    public SchemaVersionRecord setType(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.type</code>.
     */
    public String getType() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.schema_version.script</code>.
     */
    public SchemaVersionRecord setScript(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.script</code>.
     */
    public String getScript() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.schema_version.checksum</code>.
     */
    public SchemaVersionRecord setChecksum(Integer value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.checksum</code>.
     */
    public Integer getChecksum() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>stevedb.schema_version.installed_by</code>.
     */
    public SchemaVersionRecord setInstalledBy(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.installed_by</code>.
     */
    public String getInstalledBy() {
        return (String) get(6);
    }

    /**
     * Setter for <code>stevedb.schema_version.installed_on</code>.
     */
    public SchemaVersionRecord setInstalledOn(DateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.installed_on</code>.
     */
    public DateTime getInstalledOn() {
        return (DateTime) get(7);
    }

    /**
     * Setter for <code>stevedb.schema_version.execution_time</code>.
     */
    public SchemaVersionRecord setExecutionTime(Integer value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.execution_time</code>.
     */
    public Integer getExecutionTime() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>stevedb.schema_version.success</code>.
     */
    public SchemaVersionRecord setSuccess(Boolean value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.schema_version.success</code>.
     */
    public Boolean getSuccess() {
        return (Boolean) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SchemaVersionRecord
     */
    public SchemaVersionRecord() {
        super(SchemaVersion.SCHEMA_VERSION);
    }

    /**
     * Create a detached, initialised SchemaVersionRecord
     */
    public SchemaVersionRecord(Integer installedRank, String version, String description, String type, String script, Integer checksum, String installedBy, DateTime installedOn, Integer executionTime, Boolean success) {
        super(SchemaVersion.SCHEMA_VERSION);

        setInstalledRank(installedRank);
        setVersion(version);
        setDescription(description);
        setType(type);
        setScript(script);
        setChecksum(checksum);
        setInstalledBy(installedBy);
        setInstalledOn(installedOn);
        setExecutionTime(executionTime);
        setSuccess(success);
        resetChangedOnNotNull();
    }
}
