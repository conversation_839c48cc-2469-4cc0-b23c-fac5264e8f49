/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Indexes;
import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.Address.AddressPath;
import jooq.steve.db.tables.Connector.ConnectorPath;
import jooq.steve.db.tables.records.ChargeBoxRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChargeBox extends TableImpl<ChargeBoxRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.charge_box</code>
     */
    public static final ChargeBox CHARGE_BOX = new ChargeBox();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ChargeBoxRecord> getRecordType() {
        return ChargeBoxRecord.class;
    }

    /**
     * The column <code>stevedb.charge_box.charge_box_pk</code>.
     */
    public final TableField<ChargeBoxRecord, Integer> CHARGE_BOX_PK = createField(DSL.name("charge_box_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.charge_box.charge_box_id</code>.
     */
    public final TableField<ChargeBoxRecord, String> CHARGE_BOX_ID = createField(DSL.name("charge_box_id"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.charge_box.endpoint_address</code>.
     */
    public final TableField<ChargeBoxRecord, String> ENDPOINT_ADDRESS = createField(DSL.name("endpoint_address"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.ocpp_protocol</code>.
     */
    public final TableField<ChargeBoxRecord, String> OCPP_PROTOCOL = createField(DSL.name("ocpp_protocol"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.registration_status</code>.
     */
    public final TableField<ChargeBoxRecord, String> REGISTRATION_STATUS = createField(DSL.name("registration_status"), SQLDataType.VARCHAR(255).nullable(false).defaultValue(DSL.inline("'Accepted'", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.charge_point_vendor</code>.
     */
    public final TableField<ChargeBoxRecord, String> CHARGE_POINT_VENDOR = createField(DSL.name("charge_point_vendor"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.charge_point_model</code>.
     */
    public final TableField<ChargeBoxRecord, String> CHARGE_POINT_MODEL = createField(DSL.name("charge_point_model"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.charge_point_serial_number</code>.
     */
    public final TableField<ChargeBoxRecord, String> CHARGE_POINT_SERIAL_NUMBER = createField(DSL.name("charge_point_serial_number"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.charge_box_serial_number</code>.
     */
    public final TableField<ChargeBoxRecord, String> CHARGE_BOX_SERIAL_NUMBER = createField(DSL.name("charge_box_serial_number"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.fw_version</code>.
     */
    public final TableField<ChargeBoxRecord, String> FW_VERSION = createField(DSL.name("fw_version"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.fw_update_status</code>.
     */
    public final TableField<ChargeBoxRecord, String> FW_UPDATE_STATUS = createField(DSL.name("fw_update_status"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.fw_update_timestamp</code>.
     */
    public final TableField<ChargeBoxRecord, DateTime> FW_UPDATE_TIMESTAMP = createField(DSL.name("fw_update_timestamp"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.charge_box.iccid</code>.
     */
    public final TableField<ChargeBoxRecord, String> ICCID = createField(DSL.name("iccid"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.imsi</code>.
     */
    public final TableField<ChargeBoxRecord, String> IMSI = createField(DSL.name("imsi"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.meter_type</code>.
     */
    public final TableField<ChargeBoxRecord, String> METER_TYPE = createField(DSL.name("meter_type"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.meter_serial_number</code>.
     */
    public final TableField<ChargeBoxRecord, String> METER_SERIAL_NUMBER = createField(DSL.name("meter_serial_number"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.diagnostics_status</code>.
     */
    public final TableField<ChargeBoxRecord, String> DIAGNOSTICS_STATUS = createField(DSL.name("diagnostics_status"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.charge_box.diagnostics_timestamp</code>.
     */
    public final TableField<ChargeBoxRecord, DateTime> DIAGNOSTICS_TIMESTAMP = createField(DSL.name("diagnostics_timestamp"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.charge_box.last_heartbeat_timestamp</code>.
     */
    public final TableField<ChargeBoxRecord, DateTime> LAST_HEARTBEAT_TIMESTAMP = createField(DSL.name("last_heartbeat_timestamp"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.charge_box.description</code>.
     */
    public final TableField<ChargeBoxRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    /**
     * The column <code>stevedb.charge_box.note</code>.
     */
    public final TableField<ChargeBoxRecord, String> NOTE = createField(DSL.name("note"), SQLDataType.CLOB.defaultValue(DSL.inline("NULL", SQLDataType.CLOB)), this, "");

    /**
     * The column <code>stevedb.charge_box.location_latitude</code>.
     */
    public final TableField<ChargeBoxRecord, BigDecimal> LOCATION_LATITUDE = createField(DSL.name("location_latitude"), SQLDataType.DECIMAL(11, 8).defaultValue(DSL.inline("NULL", SQLDataType.DECIMAL)), this, "");

    /**
     * The column <code>stevedb.charge_box.location_longitude</code>.
     */
    public final TableField<ChargeBoxRecord, BigDecimal> LOCATION_LONGITUDE = createField(DSL.name("location_longitude"), SQLDataType.DECIMAL(11, 8).defaultValue(DSL.inline("NULL", SQLDataType.DECIMAL)), this, "");

    /**
     * The column <code>stevedb.charge_box.address_pk</code>.
     */
    public final TableField<ChargeBoxRecord, Integer> ADDRESS_PK = createField(DSL.name("address_pk"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>stevedb.charge_box.admin_address</code>.
     */
    public final TableField<ChargeBoxRecord, String> ADMIN_ADDRESS = createField(DSL.name("admin_address"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column
     * <code>stevedb.charge_box.insert_connector_status_after_transaction_msg</code>.
     */
    public final TableField<ChargeBoxRecord, Boolean> INSERT_CONNECTOR_STATUS_AFTER_TRANSACTION_MSG = createField(DSL.name("insert_connector_status_after_transaction_msg"), SQLDataType.BOOLEAN.defaultValue(DSL.inline("1", SQLDataType.BOOLEAN)), this, "");

    private ChargeBox(Name alias, Table<ChargeBoxRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ChargeBox(Name alias, Table<ChargeBoxRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.charge_box</code> table reference
     */
    public ChargeBox(String alias) {
        this(DSL.name(alias), CHARGE_BOX);
    }

    /**
     * Create an aliased <code>stevedb.charge_box</code> table reference
     */
    public ChargeBox(Name alias) {
        this(alias, CHARGE_BOX);
    }

    /**
     * Create a <code>stevedb.charge_box</code> table reference
     */
    public ChargeBox() {
        this(DSL.name("charge_box"), null);
    }

    public <O extends Record> ChargeBox(Table<O> path, ForeignKey<O, ChargeBoxRecord> childPath, InverseForeignKey<O, ChargeBoxRecord> parentPath) {
        super(path, childPath, parentPath, CHARGE_BOX);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ChargeBoxPath extends ChargeBox implements Path<ChargeBoxRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ChargeBoxPath(Table<O> path, ForeignKey<O, ChargeBoxRecord> childPath, InverseForeignKey<O, ChargeBoxRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ChargeBoxPath(Name alias, Table<ChargeBoxRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ChargeBoxPath as(String alias) {
            return new ChargeBoxPath(DSL.name(alias), this);
        }

        @Override
        public ChargeBoxPath as(Name alias) {
            return new ChargeBoxPath(alias, this);
        }

        @Override
        public ChargeBoxPath as(Table<?> alias) {
            return new ChargeBoxPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.CHARGE_BOX_CHARGEBOX_OP_EP_IDX);
    }

    @Override
    public Identity<ChargeBoxRecord, Integer> getIdentity() {
        return (Identity<ChargeBoxRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<ChargeBoxRecord> getPrimaryKey() {
        return Keys.KEY_CHARGE_BOX_PRIMARY;
    }

    @Override
    public List<UniqueKey<ChargeBoxRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_CHARGE_BOX_CHARGEBOXID_UNIQUE);
    }

    @Override
    public List<ForeignKey<ChargeBoxRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_CHARGE_BOX_ADDRESS_APK);
    }

    private transient AddressPath _address;

    /**
     * Get the implicit join path to the <code>stevedb.address</code> table.
     */
    public AddressPath address() {
        if (_address == null)
            _address = new AddressPath(this, Keys.FK_CHARGE_BOX_ADDRESS_APK, null);

        return _address;
    }

    private transient ConnectorPath _connector;

    /**
     * Get the implicit to-many join path to the <code>stevedb.connector</code>
     * table
     */
    public ConnectorPath connector() {
        if (_connector == null)
            _connector = new ConnectorPath(this, null, Keys.FK_CONNECTOR_CHARGE_BOX_CBID.getInverseKey());

        return _connector;
    }

    @Override
    public ChargeBox as(String alias) {
        return new ChargeBox(DSL.name(alias), this);
    }

    @Override
    public ChargeBox as(Name alias) {
        return new ChargeBox(alias, this);
    }

    @Override
    public ChargeBox as(Table<?> alias) {
        return new ChargeBox(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargeBox rename(String name) {
        return new ChargeBox(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargeBox rename(Name name) {
        return new ChargeBox(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargeBox rename(Table<?> name) {
        return new ChargeBox(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargeBox where(Condition condition) {
        return new ChargeBox(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargeBox where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargeBox where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargeBox where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargeBox where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargeBox where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargeBox where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargeBox where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargeBox whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargeBox whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
