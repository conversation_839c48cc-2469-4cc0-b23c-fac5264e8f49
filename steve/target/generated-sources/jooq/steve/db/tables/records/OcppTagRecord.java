/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.OcppTag;

import org.joda.time.DateTime;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OcppTagRecord extends UpdatableRecordImpl<OcppTagRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.ocpp_tag.ocpp_tag_pk</code>.
     */
    public OcppTagRecord setOcppTagPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag.ocpp_tag_pk</code>.
     */
    public Integer getOcppTagPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag.id_tag</code>.
     */
    public OcppTagRecord setIdTag(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag.id_tag</code>.
     */
    public String getIdTag() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag.parent_id_tag</code>.
     */
    public OcppTagRecord setParentIdTag(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag.parent_id_tag</code>.
     */
    public String getParentIdTag() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag.expiry_date</code>.
     */
    public OcppTagRecord setExpiryDate(DateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag.expiry_date</code>.
     */
    public DateTime getExpiryDate() {
        return (DateTime) get(3);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag.max_active_transaction_count</code>.
     */
    public OcppTagRecord setMaxActiveTransactionCount(Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag.max_active_transaction_count</code>.
     */
    public Integer getMaxActiveTransactionCount() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>stevedb.ocpp_tag.note</code>.
     */
    public OcppTagRecord setNote(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.ocpp_tag.note</code>.
     */
    public String getNote() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OcppTagRecord
     */
    public OcppTagRecord() {
        super(OcppTag.OCPP_TAG);
    }

    /**
     * Create a detached, initialised OcppTagRecord
     */
    public OcppTagRecord(Integer ocppTagPk, String idTag, String parentIdTag, DateTime expiryDate, Integer maxActiveTransactionCount, String note) {
        super(OcppTag.OCPP_TAG);

        setOcppTagPk(ocppTagPk);
        setIdTag(idTag);
        setParentIdTag(parentIdTag);
        setExpiryDate(expiryDate);
        setMaxActiveTransactionCount(maxActiveTransactionCount);
        setNote(note);
        resetChangedOnNotNull();
    }
}
