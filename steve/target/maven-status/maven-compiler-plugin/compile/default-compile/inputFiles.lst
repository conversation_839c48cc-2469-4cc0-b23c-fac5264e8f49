/code/src/main/java/de/rwth/idsg/steve/Application.java
/code/src/main/java/de/rwth/idsg/steve/ApplicationProfile.java
/code/src/main/java/de/rwth/idsg/steve/JettyServer.java
/code/src/main/java/de/rwth/idsg/steve/NotificationFeature.java
/code/src/main/java/de/rwth/idsg/steve/SteveAppContext.java
/code/src/main/java/de/rwth/idsg/steve/SteveConfiguration.java
/code/src/main/java/de/rwth/idsg/steve/SteveException.java
/code/src/main/java/de/rwth/idsg/steve/config/ApiAuthenticationManager.java
/code/src/main/java/de/rwth/idsg/steve/config/ApiDocsConfiguration.java
/code/src/main/java/de/rwth/idsg/steve/config/BeanConfiguration.java
/code/src/main/java/de/rwth/idsg/steve/config/DelegatingTaskExecutor.java
/code/src/main/java/de/rwth/idsg/steve/config/DelegatingTaskScheduler.java
/code/src/main/java/de/rwth/idsg/steve/config/OcppConfiguration.java
/code/src/main/java/de/rwth/idsg/steve/config/SecurityConfiguration.java
/code/src/main/java/de/rwth/idsg/steve/config/WebSocketConfiguration.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ChargePointServiceInvoker.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ChargePointServiceInvokerImpl.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/CommunicationTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/Ocpp15AndAboveTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/Ocpp16AndAboveTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/OcppCallback.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/OcppProtocol.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/OcppTransport.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/OcppVersion.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/RequestResult.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/TaskOrigin.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/converter/Convert.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/converter/Server12to15.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/converter/Server12to15Impl.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/converter/Server15to16.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/converter/Server15to16Impl.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/CentralSystemService12_SoapServer.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/CentralSystemService15_SoapServer.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/CentralSystemService16_SoapServer.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/ChargePointServiceSoapInvoker.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/ClientProvider.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/ClientProviderWithCache.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/LoggingFeatureProxy.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/MediatorInInterceptor.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/MessageHeaderInterceptor.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/soap/MessageIdInterceptor.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/CancelReservationTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/ChangeAvailabilityTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/ChangeConfigurationTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/ClearCacheTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/ClearChargingProfileTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/DataTransferTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/GetCompositeScheduleTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/GetConfigurationTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/GetDiagnosticsTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/GetLocalListVersionTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/RemoteStartTransactionTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/RemoteStopTransactionTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/ReserveNowTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/ResetTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/SendLocalListTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/SetChargingProfileTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/SetChargingProfileTaskAdhoc.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/SetChargingProfileTaskFromDB.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/TriggerMessageTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/UnlockConnectorTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/task/UpdateFirmwareTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/AbstractTypeStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/AbstractWebSocketEndpoint.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ChargePointServiceJsonInvoker.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ConcurrentWebSocketHandler.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ErrorFactory.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/FutureResponseContextStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/FutureResponseContextStoreImpl.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/JsonObjectMapper.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/OcppWebSocketHandshakeHandler.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/PingTask.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/SessionContextStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/SessionContextStoreImpl.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/TypeStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/WebSocketLogger.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/CustomStringModule.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/EnumMixin.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/EnumProcessor.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/MeterValue15Deserializer.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/MeterValue15Mixin.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/WsSessionSelectStrategy.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/custom/WsSessionSelectStrategyEnum.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/ActionResponsePair.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/CommunicationContext.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/ErrorCode.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/FutureResponseContext.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/MessageType.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/OcppJsonCall.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/OcppJsonError.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/OcppJsonMessage.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/OcppJsonResponse.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/OcppJsonResult.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/data/SessionContext.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp12/Ocpp12JacksonModule.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp12/Ocpp12TypeStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp12/Ocpp12WebSocketEndpoint.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp15/Ocpp15JacksonModule.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp15/Ocpp15TypeStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp15/Ocpp15WebSocketEndpoint.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp16/Ocpp16JacksonModule.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp16/Ocpp16TypeStore.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/ocpp16/Ocpp16WebSocketEndpoint.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/pipeline/AbstractCallHandler.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/pipeline/Deserializer.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/pipeline/IncomingPipeline.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/pipeline/OutgoingCallPipeline.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/pipeline/Sender.java
/code/src/main/java/de/rwth/idsg/steve/ocpp/ws/pipeline/Serializer.java
/code/src/main/java/de/rwth/idsg/steve/repository/AddressRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/ChargePointRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/ChargingProfileRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/GenericRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/OcppServerRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/OcppTagRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/ReservationRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/ReservationStatus.java
/code/src/main/java/de/rwth/idsg/steve/repository/SettingsRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/TaskStore.java
/code/src/main/java/de/rwth/idsg/steve/repository/TransactionRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/UserRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/WebUserRepository.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/ChargePoint.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/ChargePointSelect.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/ChargingProfile.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/ChargingProfileAssignment.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/ConnectorStatus.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/DbVersion.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/InsertConnectorStatusParams.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/InsertReservationParams.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/InsertTransactionParams.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/MailSettings.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/OcppTag.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/Reservation.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/TaskOverview.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/Transaction.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/TransactionDetails.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/TransactionStatusUpdate.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/UpdateChargeboxParams.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/UpdateTransactionParams.java
/code/src/main/java/de/rwth/idsg/steve/repository/dto/User.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/AddressRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/ChargePointRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/ChargingProfileRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/GenericRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/OcppServerRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/OcppTagRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/ReservationRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/SettingsRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/TaskStoreImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/TransactionRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/UserRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/repository/impl/WebUserRepositoryImpl.java
/code/src/main/java/de/rwth/idsg/steve/service/AuthTagService.java
/code/src/main/java/de/rwth/idsg/steve/service/AuthTagServiceLocal.java
/code/src/main/java/de/rwth/idsg/steve/service/BackgroundService.java
/code/src/main/java/de/rwth/idsg/steve/service/CentralSystemService16_Service.java
/code/src/main/java/de/rwth/idsg/steve/service/ChargePointHelperService.java
/code/src/main/java/de/rwth/idsg/steve/service/ChargePointServiceClient.java
/code/src/main/java/de/rwth/idsg/steve/service/DummyReleaseCheckService.java
/code/src/main/java/de/rwth/idsg/steve/service/GithubReleaseCheckService.java
/code/src/main/java/de/rwth/idsg/steve/service/MailService.java
/code/src/main/java/de/rwth/idsg/steve/service/MailServiceDefault.java
/code/src/main/java/de/rwth/idsg/steve/service/NotificationService.java
/code/src/main/java/de/rwth/idsg/steve/service/OcppTagService.java
/code/src/main/java/de/rwth/idsg/steve/service/ReleaseCheckService.java
/code/src/main/java/de/rwth/idsg/steve/service/TransactionStopService.java
/code/src/main/java/de/rwth/idsg/steve/service/UnidentifiedIncomingObjectService.java
/code/src/main/java/de/rwth/idsg/steve/service/WebUserService.java
/code/src/main/java/de/rwth/idsg/steve/service/dto/EnhancedReserveNowParams.java
/code/src/main/java/de/rwth/idsg/steve/service/dto/UnidentifiedIncomingObject.java
/code/src/main/java/de/rwth/idsg/steve/service/notification/OccpStationBooted.java
/code/src/main/java/de/rwth/idsg/steve/service/notification/OcppStationStatusFailure.java
/code/src/main/java/de/rwth/idsg/steve/service/notification/OcppStationWebSocketConnected.java
/code/src/main/java/de/rwth/idsg/steve/service/notification/OcppStationWebSocketDisconnected.java
/code/src/main/java/de/rwth/idsg/steve/service/notification/OcppTransactionEnded.java
/code/src/main/java/de/rwth/idsg/steve/service/notification/OcppTransactionStarted.java
/code/src/main/java/de/rwth/idsg/steve/utils/ConnectorStatusCountFilter.java
/code/src/main/java/de/rwth/idsg/steve/utils/ConnectorStatusFilter.java
/code/src/main/java/de/rwth/idsg/steve/utils/ControllerHelper.java
/code/src/main/java/de/rwth/idsg/steve/utils/CountryCodesProvider.java
/code/src/main/java/de/rwth/idsg/steve/utils/CustomDSL.java
/code/src/main/java/de/rwth/idsg/steve/utils/DateConverter.java
/code/src/main/java/de/rwth/idsg/steve/utils/DateTimeConverter.java
/code/src/main/java/de/rwth/idsg/steve/utils/DateTimeUtils.java
/code/src/main/java/de/rwth/idsg/steve/utils/InternetChecker.java
/code/src/main/java/de/rwth/idsg/steve/utils/LogFileRetriever.java
/code/src/main/java/de/rwth/idsg/steve/utils/OcppTagActivityRecordUtils.java
/code/src/main/java/de/rwth/idsg/steve/utils/PropertiesFileLoader.java
/code/src/main/java/de/rwth/idsg/steve/utils/StringUtils.java
/code/src/main/java/de/rwth/idsg/steve/utils/TransactionStopServiceHelper.java
/code/src/main/java/de/rwth/idsg/steve/utils/mapper/AddressMapper.java
/code/src/main/java/de/rwth/idsg/steve/utils/mapper/ChargePointDetailsMapper.java
/code/src/main/java/de/rwth/idsg/steve/utils/mapper/ChargingProfileDetailsMapper.java
/code/src/main/java/de/rwth/idsg/steve/utils/mapper/OcppTagFormMapper.java
/code/src/main/java/de/rwth/idsg/steve/utils/mapper/UserFormMapper.java
/code/src/main/java/de/rwth/idsg/steve/web/BatchInsertConverter.java
/code/src/main/java/de/rwth/idsg/steve/web/ChargePointSelectEditor.java
/code/src/main/java/de/rwth/idsg/steve/web/GlobalControllerAdvice.java
/code/src/main/java/de/rwth/idsg/steve/web/LocalDateEditor.java
/code/src/main/java/de/rwth/idsg/steve/web/LocalDateTimeEditor.java
/code/src/main/java/de/rwth/idsg/steve/web/api/ApiControllerAdvice.java
/code/src/main/java/de/rwth/idsg/steve/web/api/OcppTagsRestController.java
/code/src/main/java/de/rwth/idsg/steve/web/api/TransactionsRestController.java
/code/src/main/java/de/rwth/idsg/steve/web/api/exception/BadRequestException.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/AboutSettingsController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/AjaxCallController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/ChargePointsController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/ChargingProfilesController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/HomeController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/LogController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/Ocpp12Controller.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/Ocpp15Controller.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/Ocpp16Controller.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/OcppTagsController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/SignOutController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/TaskController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/TransactionsReservationsController.java
/code/src/main/java/de/rwth/idsg/steve/web/controller/UsersController.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/Address.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ChargePointBatchInsertForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ChargePointForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ChargePointQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ChargingProfileAssignmentQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ChargingProfileForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ChargingProfileQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ConnectorStatusForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/EndpointInfo.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/OcppJsonStatus.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/OcppTagBatchInsertForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/OcppTagForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/OcppTagQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/QueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ReleaseReport.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ReleaseResponse.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ReservationQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/SettingsForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/Statistics.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/TransactionQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/UserForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/UserQueryForm.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/UserSex.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/AvailabilityType.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/CancelReservationParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ChangeAvailabilityParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ChangeConfigurationParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ChargePointSelection.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ClearChargingProfileFilterType.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ClearChargingProfileParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ConfigurationKeyEnum.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ConfigurationKeyReadWriteEnum.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/DataTransferParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/GetCompositeScheduleParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/GetConfigurationParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/GetDiagnosticsParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/MultipleChargePointSelect.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/RemoteStartTransactionParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/RemoteStopTransactionParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ReserveNowParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ResetParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/ResetType.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/SendLocalListParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/SendLocalListUpdateType.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/SetChargingProfileParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/SingleChargePointSelect.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/TriggerMessageEnum.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/TriggerMessageParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/UnlockConnectorParams.java
/code/src/main/java/de/rwth/idsg/steve/web/dto/ocpp/UpdateFirmwareParams.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/ChargeBoxId.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/ChargeBoxIdListValidator.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/ChargeBoxIdValidator.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/EmailCollection.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/EmailCollectionValidator.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/IdTag.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/IdTagListValidator.java
/code/src/main/java/de/rwth/idsg/steve/web/validation/IdTagValidator.java
/code/target/generated-sources/jooq/steve/db/DefaultCatalog.java
/code/target/generated-sources/jooq/steve/db/Indexes.java
/code/target/generated-sources/jooq/steve/db/Keys.java
/code/target/generated-sources/jooq/steve/db/Stevedb.java
/code/target/generated-sources/jooq/steve/db/Tables.java
/code/target/generated-sources/jooq/steve/db/enums/TransactionStopEventActor.java
/code/target/generated-sources/jooq/steve/db/enums/TransactionStopFailedEventActor.java
/code/target/generated-sources/jooq/steve/db/tables/Address.java
/code/target/generated-sources/jooq/steve/db/tables/ChargeBox.java
/code/target/generated-sources/jooq/steve/db/tables/ChargingProfile.java
/code/target/generated-sources/jooq/steve/db/tables/ChargingSchedulePeriod.java
/code/target/generated-sources/jooq/steve/db/tables/Connector.java
/code/target/generated-sources/jooq/steve/db/tables/ConnectorChargingProfile.java
/code/target/generated-sources/jooq/steve/db/tables/ConnectorMeterValue.java
/code/target/generated-sources/jooq/steve/db/tables/ConnectorStatus.java
/code/target/generated-sources/jooq/steve/db/tables/OcppTag.java
/code/target/generated-sources/jooq/steve/db/tables/OcppTagActivity.java
/code/target/generated-sources/jooq/steve/db/tables/Reservation.java
/code/target/generated-sources/jooq/steve/db/tables/SchemaVersion.java
/code/target/generated-sources/jooq/steve/db/tables/Settings.java
/code/target/generated-sources/jooq/steve/db/tables/Transaction.java
/code/target/generated-sources/jooq/steve/db/tables/TransactionStart.java
/code/target/generated-sources/jooq/steve/db/tables/TransactionStop.java
/code/target/generated-sources/jooq/steve/db/tables/TransactionStopFailed.java
/code/target/generated-sources/jooq/steve/db/tables/User.java
/code/target/generated-sources/jooq/steve/db/tables/WebUser.java
/code/target/generated-sources/jooq/steve/db/tables/records/AddressRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ChargeBoxRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ChargingProfileRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ChargingSchedulePeriodRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ConnectorChargingProfileRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ConnectorMeterValueRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ConnectorRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ConnectorStatusRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/OcppTagActivityRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/OcppTagRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/ReservationRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/SchemaVersionRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/SettingsRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/TransactionRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/TransactionStartRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/TransactionStopFailedRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/TransactionStopRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/UserRecord.java
/code/target/generated-sources/jooq/steve/db/tables/records/WebUserRecord.java
