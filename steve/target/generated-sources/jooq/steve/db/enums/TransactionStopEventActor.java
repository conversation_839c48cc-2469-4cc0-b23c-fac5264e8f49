/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.enums;


import org.jooq.Catalog;
import org.jooq.EnumType;
import org.jooq.Schema;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public enum TransactionStopEventActor implements EnumType {

    station("station"),

    manual("manual");

    private final String literal;

    private TransactionStopEventActor(String literal) {
        this.literal = literal;
    }

    @Override
    public Catalog getCatalog() {
        return null;
    }

    @Override
    public Schema getSchema() {
        return null;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getLiteral() {
        return literal;
    }

    /**
     * Lookup a value of this EnumType by its literal. Returns
     * <code>null</code>, if no such value could be found, see {@link
     * EnumType#lookupLiteral(Class, String)}.
     */
    public static TransactionStopEventActor lookupLiteral(String literal) {
        return EnumType.lookupLiteral(TransactionStopEventActor.class, literal);
    }
}
