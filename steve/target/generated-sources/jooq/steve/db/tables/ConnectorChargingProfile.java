/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.ChargingProfile.ChargingProfilePath;
import jooq.steve.db.tables.Connector.ConnectorPath;
import jooq.steve.db.tables.records.ConnectorChargingProfileRecord;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorChargingProfile extends TableImpl<ConnectorChargingProfileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.connector_charging_profile</code>
     */
    public static final ConnectorChargingProfile CONNECTOR_CHARGING_PROFILE = new ConnectorChargingProfile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConnectorChargingProfileRecord> getRecordType() {
        return ConnectorChargingProfileRecord.class;
    }

    /**
     * The column <code>stevedb.connector_charging_profile.connector_pk</code>.
     */
    public final TableField<ConnectorChargingProfileRecord, Integer> CONNECTOR_PK = createField(DSL.name("connector_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column
     * <code>stevedb.connector_charging_profile.charging_profile_pk</code>.
     */
    public final TableField<ConnectorChargingProfileRecord, Integer> CHARGING_PROFILE_PK = createField(DSL.name("charging_profile_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    private ConnectorChargingProfile(Name alias, Table<ConnectorChargingProfileRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ConnectorChargingProfile(Name alias, Table<ConnectorChargingProfileRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.connector_charging_profile</code> table
     * reference
     */
    public ConnectorChargingProfile(String alias) {
        this(DSL.name(alias), CONNECTOR_CHARGING_PROFILE);
    }

    /**
     * Create an aliased <code>stevedb.connector_charging_profile</code> table
     * reference
     */
    public ConnectorChargingProfile(Name alias) {
        this(alias, CONNECTOR_CHARGING_PROFILE);
    }

    /**
     * Create a <code>stevedb.connector_charging_profile</code> table reference
     */
    public ConnectorChargingProfile() {
        this(DSL.name("connector_charging_profile"), null);
    }

    public <O extends Record> ConnectorChargingProfile(Table<O> path, ForeignKey<O, ConnectorChargingProfileRecord> childPath, InverseForeignKey<O, ConnectorChargingProfileRecord> parentPath) {
        super(path, childPath, parentPath, CONNECTOR_CHARGING_PROFILE);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ConnectorChargingProfilePath extends ConnectorChargingProfile implements Path<ConnectorChargingProfileRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ConnectorChargingProfilePath(Table<O> path, ForeignKey<O, ConnectorChargingProfileRecord> childPath, InverseForeignKey<O, ConnectorChargingProfileRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ConnectorChargingProfilePath(Name alias, Table<ConnectorChargingProfileRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ConnectorChargingProfilePath as(String alias) {
            return new ConnectorChargingProfilePath(DSL.name(alias), this);
        }

        @Override
        public ConnectorChargingProfilePath as(Name alias) {
            return new ConnectorChargingProfilePath(alias, this);
        }

        @Override
        public ConnectorChargingProfilePath as(Table<?> alias) {
            return new ConnectorChargingProfilePath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<UniqueKey<ConnectorChargingProfileRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_CONNECTOR_CHARGING_PROFILE_UQ_CONNECTOR_CHARGING_PROFILE);
    }

    @Override
    public List<ForeignKey<ConnectorChargingProfileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_CONNECTOR_CHARGING_PROFILE_CHARGING_PROFILE_PK, Keys.FK_CONNECTOR_CHARGING_PROFILE_CONNECTOR_PK);
    }

    private transient ChargingProfilePath _chargingProfile;

    /**
     * Get the implicit join path to the <code>stevedb.charging_profile</code>
     * table.
     */
    public ChargingProfilePath chargingProfile() {
        if (_chargingProfile == null)
            _chargingProfile = new ChargingProfilePath(this, Keys.FK_CONNECTOR_CHARGING_PROFILE_CHARGING_PROFILE_PK, null);

        return _chargingProfile;
    }

    private transient ConnectorPath _connector;

    /**
     * Get the implicit join path to the <code>stevedb.connector</code> table.
     */
    public ConnectorPath connector() {
        if (_connector == null)
            _connector = new ConnectorPath(this, Keys.FK_CONNECTOR_CHARGING_PROFILE_CONNECTOR_PK, null);

        return _connector;
    }

    @Override
    public ConnectorChargingProfile as(String alias) {
        return new ConnectorChargingProfile(DSL.name(alias), this);
    }

    @Override
    public ConnectorChargingProfile as(Name alias) {
        return new ConnectorChargingProfile(alias, this);
    }

    @Override
    public ConnectorChargingProfile as(Table<?> alias) {
        return new ConnectorChargingProfile(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorChargingProfile rename(String name) {
        return new ConnectorChargingProfile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorChargingProfile rename(Name name) {
        return new ConnectorChargingProfile(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ConnectorChargingProfile rename(Table<?> name) {
        return new ConnectorChargingProfile(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorChargingProfile where(Condition condition) {
        return new ConnectorChargingProfile(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorChargingProfile where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorChargingProfile where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorChargingProfile where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorChargingProfile where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorChargingProfile where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorChargingProfile where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ConnectorChargingProfile where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorChargingProfile whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ConnectorChargingProfile whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
