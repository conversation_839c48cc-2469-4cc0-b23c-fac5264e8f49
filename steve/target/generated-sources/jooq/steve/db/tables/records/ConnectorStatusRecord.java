/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.ConnectorStatus;

import org.joda.time.DateTime;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConnectorStatusRecord extends TableRecordImpl<ConnectorStatusRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.connector_status.connector_pk</code>.
     */
    public ConnectorStatusRecord setConnectorPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.connector_pk</code>.
     */
    public Integer getConnectorPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.connector_status.status_timestamp</code>.
     */
    public ConnectorStatusRecord setStatusTimestamp(DateTime value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.status_timestamp</code>.
     */
    public DateTime getStatusTimestamp() {
        return (DateTime) get(1);
    }

    /**
     * Setter for <code>stevedb.connector_status.status</code>.
     */
    public ConnectorStatusRecord setStatus(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.status</code>.
     */
    public String getStatus() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.connector_status.error_code</code>.
     */
    public ConnectorStatusRecord setErrorCode(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.error_code</code>.
     */
    public String getErrorCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.connector_status.error_info</code>.
     */
    public ConnectorStatusRecord setErrorInfo(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.error_info</code>.
     */
    public String getErrorInfo() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.connector_status.vendor_id</code>.
     */
    public ConnectorStatusRecord setVendorId(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.vendor_id</code>.
     */
    public String getVendorId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>stevedb.connector_status.vendor_error_code</code>.
     */
    public ConnectorStatusRecord setVendorErrorCode(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.connector_status.vendor_error_code</code>.
     */
    public String getVendorErrorCode() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConnectorStatusRecord
     */
    public ConnectorStatusRecord() {
        super(ConnectorStatus.CONNECTOR_STATUS);
    }

    /**
     * Create a detached, initialised ConnectorStatusRecord
     */
    public ConnectorStatusRecord(Integer connectorPk, DateTime statusTimestamp, String status, String errorCode, String errorInfo, String vendorId, String vendorErrorCode) {
        super(ConnectorStatus.CONNECTOR_STATUS);

        setConnectorPk(connectorPk);
        setStatusTimestamp(statusTimestamp);
        setStatus(status);
        setErrorCode(errorCode);
        setErrorInfo(errorInfo);
        setVendorId(vendorId);
        setVendorErrorCode(vendorErrorCode);
        resetChangedOnNotNull();
    }
}
