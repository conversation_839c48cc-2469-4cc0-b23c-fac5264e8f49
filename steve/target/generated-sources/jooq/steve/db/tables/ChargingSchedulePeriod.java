/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.ChargingProfile.ChargingProfilePath;
import jooq.steve.db.tables.records.ChargingSchedulePeriodRecord;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChargingSchedulePeriod extends TableImpl<ChargingSchedulePeriodRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.charging_schedule_period</code>
     */
    public static final ChargingSchedulePeriod CHARGING_SCHEDULE_PERIOD = new ChargingSchedulePeriod();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ChargingSchedulePeriodRecord> getRecordType() {
        return ChargingSchedulePeriodRecord.class;
    }

    /**
     * The column
     * <code>stevedb.charging_schedule_period.charging_profile_pk</code>.
     */
    public final TableField<ChargingSchedulePeriodRecord, Integer> CHARGING_PROFILE_PK = createField(DSL.name("charging_profile_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column
     * <code>stevedb.charging_schedule_period.start_period_in_seconds</code>.
     */
    public final TableField<ChargingSchedulePeriodRecord, Integer> START_PERIOD_IN_SECONDS = createField(DSL.name("start_period_in_seconds"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column <code>stevedb.charging_schedule_period.power_limit</code>.
     */
    public final TableField<ChargingSchedulePeriodRecord, BigDecimal> POWER_LIMIT = createField(DSL.name("power_limit"), SQLDataType.DECIMAL(15, 1).nullable(false), this, "");

    /**
     * The column <code>stevedb.charging_schedule_period.number_phases</code>.
     */
    public final TableField<ChargingSchedulePeriodRecord, Integer> NUMBER_PHASES = createField(DSL.name("number_phases"), SQLDataType.INTEGER.defaultValue(DSL.inline("NULL", SQLDataType.INTEGER)), this, "");

    private ChargingSchedulePeriod(Name alias, Table<ChargingSchedulePeriodRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private ChargingSchedulePeriod(Name alias, Table<ChargingSchedulePeriodRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.charging_schedule_period</code> table
     * reference
     */
    public ChargingSchedulePeriod(String alias) {
        this(DSL.name(alias), CHARGING_SCHEDULE_PERIOD);
    }

    /**
     * Create an aliased <code>stevedb.charging_schedule_period</code> table
     * reference
     */
    public ChargingSchedulePeriod(Name alias) {
        this(alias, CHARGING_SCHEDULE_PERIOD);
    }

    /**
     * Create a <code>stevedb.charging_schedule_period</code> table reference
     */
    public ChargingSchedulePeriod() {
        this(DSL.name("charging_schedule_period"), null);
    }

    public <O extends Record> ChargingSchedulePeriod(Table<O> path, ForeignKey<O, ChargingSchedulePeriodRecord> childPath, InverseForeignKey<O, ChargingSchedulePeriodRecord> parentPath) {
        super(path, childPath, parentPath, CHARGING_SCHEDULE_PERIOD);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ChargingSchedulePeriodPath extends ChargingSchedulePeriod implements Path<ChargingSchedulePeriodRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ChargingSchedulePeriodPath(Table<O> path, ForeignKey<O, ChargingSchedulePeriodRecord> childPath, InverseForeignKey<O, ChargingSchedulePeriodRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ChargingSchedulePeriodPath(Name alias, Table<ChargingSchedulePeriodRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ChargingSchedulePeriodPath as(String alias) {
            return new ChargingSchedulePeriodPath(DSL.name(alias), this);
        }

        @Override
        public ChargingSchedulePeriodPath as(Name alias) {
            return new ChargingSchedulePeriodPath(alias, this);
        }

        @Override
        public ChargingSchedulePeriodPath as(Table<?> alias) {
            return new ChargingSchedulePeriodPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<UniqueKey<ChargingSchedulePeriodRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_CHARGING_SCHEDULE_PERIOD_UQ_CHARGING_SCHEDULE_PERIOD);
    }

    @Override
    public List<ForeignKey<ChargingSchedulePeriodRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_CHARGING_SCHEDULE_PERIOD_CHARGING_PROFILE_PK);
    }

    private transient ChargingProfilePath _chargingProfile;

    /**
     * Get the implicit join path to the <code>stevedb.charging_profile</code>
     * table.
     */
    public ChargingProfilePath chargingProfile() {
        if (_chargingProfile == null)
            _chargingProfile = new ChargingProfilePath(this, Keys.FK_CHARGING_SCHEDULE_PERIOD_CHARGING_PROFILE_PK, null);

        return _chargingProfile;
    }

    @Override
    public ChargingSchedulePeriod as(String alias) {
        return new ChargingSchedulePeriod(DSL.name(alias), this);
    }

    @Override
    public ChargingSchedulePeriod as(Name alias) {
        return new ChargingSchedulePeriod(alias, this);
    }

    @Override
    public ChargingSchedulePeriod as(Table<?> alias) {
        return new ChargingSchedulePeriod(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargingSchedulePeriod rename(String name) {
        return new ChargingSchedulePeriod(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargingSchedulePeriod rename(Name name) {
        return new ChargingSchedulePeriod(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ChargingSchedulePeriod rename(Table<?> name) {
        return new ChargingSchedulePeriod(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingSchedulePeriod where(Condition condition) {
        return new ChargingSchedulePeriod(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingSchedulePeriod where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingSchedulePeriod where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingSchedulePeriod where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingSchedulePeriod where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingSchedulePeriod where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingSchedulePeriod where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public ChargingSchedulePeriod where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingSchedulePeriod whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public ChargingSchedulePeriod whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
