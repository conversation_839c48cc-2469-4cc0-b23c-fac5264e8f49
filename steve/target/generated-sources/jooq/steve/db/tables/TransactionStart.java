/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import de.rwth.idsg.steve.utils.DateTimeConverter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Indexes;
import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.Connector.ConnectorPath;
import jooq.steve.db.tables.ConnectorMeterValue.ConnectorMeterValuePath;
import jooq.steve.db.tables.OcppTag.OcppTagPath;
import jooq.steve.db.tables.Reservation.ReservationPath;
import jooq.steve.db.tables.TransactionStop.TransactionStopPath;
import jooq.steve.db.tables.records.TransactionStartRecord;

import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionStart extends TableImpl<TransactionStartRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.transaction_start</code>
     */
    public static final TransactionStart TRANSACTION_START = new TransactionStart();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TransactionStartRecord> getRecordType() {
        return TransactionStartRecord.class;
    }

    /**
     * The column <code>stevedb.transaction_start.transaction_pk</code>.
     */
    public final TableField<TransactionStartRecord, Integer> TRANSACTION_PK = createField(DSL.name("transaction_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.transaction_start.event_timestamp</code>.
     */
    public final TableField<TransactionStartRecord, DateTime> EVENT_TIMESTAMP = createField(DSL.name("event_timestamp"), SQLDataType.TIMESTAMP(6).nullable(false).defaultValue(DSL.field(DSL.raw("current_timestamp(6)"), SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.transaction_start.connector_pk</code>.
     */
    public final TableField<TransactionStartRecord, Integer> CONNECTOR_PK = createField(DSL.name("connector_pk"), SQLDataType.INTEGER.nullable(false), this, "");

    /**
     * The column <code>stevedb.transaction_start.id_tag</code>.
     */
    public final TableField<TransactionStartRecord, String> ID_TAG = createField(DSL.name("id_tag"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.transaction_start.start_timestamp</code>.
     */
    public final TableField<TransactionStartRecord, DateTime> START_TIMESTAMP = createField(DSL.name("start_timestamp"), SQLDataType.TIMESTAMP(6).defaultValue(DSL.inline("NULL", SQLDataType.TIMESTAMP)), this, "", new DateTimeConverter());

    /**
     * The column <code>stevedb.transaction_start.start_value</code>.
     */
    public final TableField<TransactionStartRecord, String> START_VALUE = createField(DSL.name("start_value"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    private TransactionStart(Name alias, Table<TransactionStartRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private TransactionStart(Name alias, Table<TransactionStartRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.transaction_start</code> table reference
     */
    public TransactionStart(String alias) {
        this(DSL.name(alias), TRANSACTION_START);
    }

    /**
     * Create an aliased <code>stevedb.transaction_start</code> table reference
     */
    public TransactionStart(Name alias) {
        this(alias, TRANSACTION_START);
    }

    /**
     * Create a <code>stevedb.transaction_start</code> table reference
     */
    public TransactionStart() {
        this(DSL.name("transaction_start"), null);
    }

    public <O extends Record> TransactionStart(Table<O> path, ForeignKey<O, TransactionStartRecord> childPath, InverseForeignKey<O, TransactionStartRecord> parentPath) {
        super(path, childPath, parentPath, TRANSACTION_START);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class TransactionStartPath extends TransactionStart implements Path<TransactionStartRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> TransactionStartPath(Table<O> path, ForeignKey<O, TransactionStartRecord> childPath, InverseForeignKey<O, TransactionStartRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private TransactionStartPath(Name alias, Table<TransactionStartRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public TransactionStartPath as(String alias) {
            return new TransactionStartPath(DSL.name(alias), this);
        }

        @Override
        public TransactionStartPath as(Name alias) {
            return new TransactionStartPath(alias, this);
        }

        @Override
        public TransactionStartPath as(Table<?> alias) {
            return new TransactionStartPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.TRANSACTION_START_CONNECTOR_PK_IDX, Indexes.TRANSACTION_START_IDTAG_IDX, Indexes.TRANSACTION_START_TRANSACTION_START_IDX);
    }

    @Override
    public Identity<TransactionStartRecord, Integer> getIdentity() {
        return (Identity<TransactionStartRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<TransactionStartRecord> getPrimaryKey() {
        return Keys.KEY_TRANSACTION_START_PRIMARY;
    }

    @Override
    public List<UniqueKey<TransactionStartRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_TRANSACTION_START_TRANSACTION_PK_UNIQUE);
    }

    @Override
    public List<ForeignKey<TransactionStartRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_CONNECTOR_PK_T, Keys.FK_TRANSACTION_OCPP_TAG_ID_TAG);
    }

    private transient ConnectorPath _connector;

    /**
     * Get the implicit join path to the <code>stevedb.connector</code> table.
     */
    public ConnectorPath connector() {
        if (_connector == null)
            _connector = new ConnectorPath(this, Keys.FK_CONNECTOR_PK_T, null);

        return _connector;
    }

    private transient OcppTagPath _ocppTag;

    /**
     * Get the implicit join path to the <code>stevedb.ocpp_tag</code> table.
     */
    public OcppTagPath ocppTag() {
        if (_ocppTag == null)
            _ocppTag = new OcppTagPath(this, Keys.FK_TRANSACTION_OCPP_TAG_ID_TAG, null);

        return _ocppTag;
    }

    private transient ConnectorMeterValuePath _connectorMeterValue;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.connector_meter_value</code> table
     */
    public ConnectorMeterValuePath connectorMeterValue() {
        if (_connectorMeterValue == null)
            _connectorMeterValue = new ConnectorMeterValuePath(this, null, Keys.FK_TID_CM.getInverseKey());

        return _connectorMeterValue;
    }

    private transient ReservationPath _reservation;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.reservation</code> table
     */
    public ReservationPath reservation() {
        if (_reservation == null)
            _reservation = new ReservationPath(this, null, Keys.FK_TRANSACTION_PK_R.getInverseKey());

        return _reservation;
    }

    private transient TransactionStopPath _transactionStop;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.transaction_stop</code> table
     */
    public TransactionStopPath transactionStop() {
        if (_transactionStop == null)
            _transactionStop = new TransactionStopPath(this, null, Keys.FK_TRANSACTION_STOP_TRANSACTION_PK.getInverseKey());

        return _transactionStop;
    }

    @Override
    public TransactionStart as(String alias) {
        return new TransactionStart(DSL.name(alias), this);
    }

    @Override
    public TransactionStart as(Name alias) {
        return new TransactionStart(alias, this);
    }

    @Override
    public TransactionStart as(Table<?> alias) {
        return new TransactionStart(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStart rename(String name) {
        return new TransactionStart(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStart rename(Name name) {
        return new TransactionStart(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public TransactionStart rename(Table<?> name) {
        return new TransactionStart(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStart where(Condition condition) {
        return new TransactionStart(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStart where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStart where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStart where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStart where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStart where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStart where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public TransactionStart where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStart whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public TransactionStart whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
