
<!--
Automatically created by Apache Tomcat JspC.
-->


    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dcontext_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002dcontext_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.ocppJsonStatus_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.ocppJsonStatus_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002derror_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002derror_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.GetConfigurationResponse_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.GetConfigurationResponse_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileAssignments_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileAssignments_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.users_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.users_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTagDetails_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTagDetails_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.connectorStatus_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.connectorStatus_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepointAdd_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepointAdd_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.userDetails_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.userDetails_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002duser_002dprofile_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002duser_002dprofile_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002dcp_002dmisc_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002dcp_002dmisc_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.transactionDetails_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.transactionDetails_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002dcharging_002dprofile_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002dcharging_002dprofile_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.reservations_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.reservations_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTags_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTags_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepointDetails_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepointDetails_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepoints_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepoints_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.transactions_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.transactions_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileAdd_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileAdd_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfiles_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfiles_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002daddress_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002daddress_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.userAdd_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.userAdd_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002duser_002docpp_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002duser_002docpp_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002docppTag_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002docppTag_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileDetails_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileDetails_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTagAdd_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTagAdd_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.RemoteStartTransaction_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.RemoteStartTransaction_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.UpdateFirmware_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.UpdateFirmware_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetLocalListVersion_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.GetLocalListVersion_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetDiagnostics_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.GetDiagnostics_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.DataTransfer_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.DataTransfer_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetCompositeSchedule_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.GetCompositeSchedule_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.CancelReservation_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.CancelReservation_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.RemoteStopTransaction_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.RemoteStopTransaction_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.TriggerMessage_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.TriggerMessage_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ReserveNow_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.ReserveNow_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.UnlockConnector_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.UnlockConnector_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetCompositeScheduleResponse_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.GetCompositeScheduleResponse_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ChangeAvailability_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.ChangeAvailability_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ChangeConfiguration_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.ChangeConfiguration_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.SendLocalList_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.SendLocalList_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dfooter_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002dfooter_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ClearCache_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.ClearCache_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.signin_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.signin_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ClearChargingProfile_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.ClearChargingProfile_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetConfiguration_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.GetConfiguration_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.about_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.about_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dcp_002dsingle_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002dcp_002dsingle_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dheader_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002dheader_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.SetChargingProfile_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.SetChargingProfile_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.Reset_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op16.Reset_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.UpdateFirmwareForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.UpdateFirmwareForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetLocalListForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetLocalListForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.taskResult_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.taskResult_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetConfigurationForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetConfigurationForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetCompositeScheduleForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetCompositeScheduleForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.SendLocalListForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.SendLocalListForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.SetChargingProfileForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.SetChargingProfileForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ReserveNowForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.ReserveNowForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.CancelReservationForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.CancelReservationForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.TriggerMessageForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.TriggerMessageForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetDiagnosticsForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetDiagnosticsForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.DataTransferForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.DataTransferForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ChangeConfigurationForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.ChangeConfigurationForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ClearCacheForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.ClearCacheForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.RemoteStopTransactionForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.RemoteStopTransactionForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ResetForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.ResetForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.RemoteStartTransactionForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.RemoteStartTransactionForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dcp_002dmultiple_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002dcp_002dmultiple_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.UnlockConnectorForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.UnlockConnectorForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ChangeAvailabilityForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.ChangeAvailabilityForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ClearChargingProfileForm_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op_002dforms.ClearChargingProfileForm_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.CancelReservation_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.CancelReservation_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.GetDiagnostics_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.GetDiagnostics_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.GetLocalListVersion_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.GetLocalListVersion_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.RemoteStopTransaction_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.RemoteStopTransaction_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.DataTransfer_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.DataTransfer_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ChangeAvailability_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.ChangeAvailability_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.UpdateFirmware_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.UpdateFirmware_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.settings_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.settings_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.RemoteStartTransaction_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.RemoteStartTransaction_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ChangeConfiguration_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.ChangeConfiguration_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.UnlockConnector_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.UnlockConnector_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.Reset_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.Reset_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ReserveNow_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.ReserveNow_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.GetConfiguration_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.GetConfiguration_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.GetDiagnostics_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.GetDiagnostics_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.SendLocalList_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.SendLocalList_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.RemoteStopTransaction_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.RemoteStopTransaction_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ClearCache_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op15.ClearCache_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.UnlockConnector_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.UnlockConnector_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dop_002dbind_002derrors_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views._00_002dop_002dbind_002derrors_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.Reset_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.Reset_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.ClearCache_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.ClearCache_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.RemoteStartTransaction_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.RemoteStartTransaction_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.ChangeAvailability_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.ChangeAvailability_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.tasks_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.tasks_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.ChangeConfiguration_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.ChangeConfiguration_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.UpdateFirmware_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.op12.UpdateFirmware_jsp</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.home_jsp</servlet-name>
        <servlet-class>org.apache.jsp.WEB_002dINF.views.home_jsp</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dcontext_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-context.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.ocppJsonStatus_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/ocppJsonStatus.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002derror_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-error.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.GetConfigurationResponse_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/GetConfigurationResponse.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileAssignments_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargingProfileAssignments.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.users_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/users.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTagDetails_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/ocppTagDetails.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.connectorStatus_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/connectorStatus.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepointAdd_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargepointAdd.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.userDetails_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/userDetails.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002duser_002dprofile_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/00-user-profile.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002dcp_002dmisc_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/00-cp-misc.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.transactionDetails_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/transactionDetails.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002dcharging_002dprofile_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/00-charging-profile.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.reservations_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/reservations.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTags_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/ocppTags.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepointDetails_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargepointDetails.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargepoints_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargepoints.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.transactions_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/transactions.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileAdd_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargingProfileAdd.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfiles_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargingProfiles.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002daddress_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/00-address.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.userAdd_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/userAdd.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002duser_002docpp_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/00-user-ocpp.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman._00_002docppTag_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/00-ocppTag.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.chargingProfileDetails_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/chargingProfileDetails.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.data_002dman.ocppTagAdd_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/data-man/ocppTagAdd.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.RemoteStartTransaction_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/RemoteStartTransaction.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.UpdateFirmware_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/UpdateFirmware.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetLocalListVersion_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/GetLocalListVersion.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetDiagnostics_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/GetDiagnostics.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.DataTransfer_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/DataTransfer.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetCompositeSchedule_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/GetCompositeSchedule.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.CancelReservation_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/CancelReservation.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.RemoteStopTransaction_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/RemoteStopTransaction.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.TriggerMessage_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/TriggerMessage.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ReserveNow_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/ReserveNow.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.UnlockConnector_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/UnlockConnector.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetCompositeScheduleResponse_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/GetCompositeScheduleResponse.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ChangeAvailability_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/ChangeAvailability.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ChangeConfiguration_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/ChangeConfiguration.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.SendLocalList_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/SendLocalList.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dfooter_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-footer.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ClearCache_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/ClearCache.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.signin_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/signin.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.ClearChargingProfile_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/ClearChargingProfile.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.GetConfiguration_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/GetConfiguration.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.about_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/about.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dcp_002dsingle_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-cp-single.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dheader_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-header.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.SetChargingProfile_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/SetChargingProfile.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op16.Reset_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op16/Reset.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.UpdateFirmwareForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/UpdateFirmwareForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetLocalListForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/GetLocalListForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.taskResult_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/taskResult.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetConfigurationForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/GetConfigurationForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetCompositeScheduleForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/GetCompositeScheduleForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.SendLocalListForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/SendLocalListForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.SetChargingProfileForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/SetChargingProfileForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ReserveNowForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/ReserveNowForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.CancelReservationForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/CancelReservationForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.TriggerMessageForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/TriggerMessageForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.GetDiagnosticsForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/GetDiagnosticsForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.DataTransferForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/DataTransferForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ChangeConfigurationForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/ChangeConfigurationForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ClearCacheForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/ClearCacheForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.RemoteStopTransactionForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/RemoteStopTransactionForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ResetForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/ResetForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.RemoteStartTransactionForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/RemoteStartTransactionForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dcp_002dmultiple_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-cp-multiple.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.UnlockConnectorForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/UnlockConnectorForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ChangeAvailabilityForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/ChangeAvailabilityForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op_002dforms.ClearChargingProfileForm_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op-forms/ClearChargingProfileForm.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.CancelReservation_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/CancelReservation.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.GetDiagnostics_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/GetDiagnostics.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.GetLocalListVersion_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/GetLocalListVersion.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.RemoteStopTransaction_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/RemoteStopTransaction.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.DataTransfer_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/DataTransfer.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ChangeAvailability_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/ChangeAvailability.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.UpdateFirmware_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/UpdateFirmware.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.settings_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/settings.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.RemoteStartTransaction_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/RemoteStartTransaction.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ChangeConfiguration_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/ChangeConfiguration.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.UnlockConnector_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/UnlockConnector.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.Reset_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/Reset.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ReserveNow_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/ReserveNow.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.GetConfiguration_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/GetConfiguration.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.GetDiagnostics_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/GetDiagnostics.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.SendLocalList_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/SendLocalList.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.RemoteStopTransaction_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/RemoteStopTransaction.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op15.ClearCache_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op15/ClearCache.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.UnlockConnector_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/UnlockConnector.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views._00_002dop_002dbind_002derrors_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/00-op-bind-errors.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.Reset_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/Reset.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.ClearCache_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/ClearCache.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.RemoteStartTransaction_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/RemoteStartTransaction.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.ChangeAvailability_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/ChangeAvailability.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.tasks_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/tasks.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.ChangeConfiguration_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/ChangeConfiguration.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.op12.UpdateFirmware_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/op12/UpdateFirmware.jsp</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>org.apache.jsp.WEB_002dINF.views.home_jsp</servlet-name>
        <url-pattern>/WEB-INF/views/home.jsp</url-pattern>
    </servlet-mapping>

<!--
End of content automatically created by Apache Tomcat JspC.
-->

