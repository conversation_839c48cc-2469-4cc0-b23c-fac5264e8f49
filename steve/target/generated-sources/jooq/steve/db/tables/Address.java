/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import java.util.Collection;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.ChargeBox.ChargeBoxPath;
import jooq.steve.db.tables.User.UserPath;
import jooq.steve.db.tables.records.AddressRecord;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Address extends TableImpl<AddressRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.address</code>
     */
    public static final Address ADDRESS = new Address();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AddressRecord> getRecordType() {
        return AddressRecord.class;
    }

    /**
     * The column <code>stevedb.address.address_pk</code>.
     */
    public final TableField<AddressRecord, Integer> ADDRESS_PK = createField(DSL.name("address_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.address.street</code>.
     */
    public final TableField<AddressRecord, String> STREET = createField(DSL.name("street"), SQLDataType.VARCHAR(1000).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.address.house_number</code>.
     */
    public final TableField<AddressRecord, String> HOUSE_NUMBER = createField(DSL.name("house_number"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.address.zip_code</code>.
     */
    public final TableField<AddressRecord, String> ZIP_CODE = createField(DSL.name("zip_code"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.address.city</code>.
     */
    public final TableField<AddressRecord, String> CITY = createField(DSL.name("city"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>stevedb.address.country</code>.
     */
    public final TableField<AddressRecord, String> COUNTRY = createField(DSL.name("country"), SQLDataType.VARCHAR(255).defaultValue(DSL.inline("NULL", SQLDataType.VARCHAR)), this, "");

    private Address(Name alias, Table<AddressRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private Address(Name alias, Table<AddressRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.address</code> table reference
     */
    public Address(String alias) {
        this(DSL.name(alias), ADDRESS);
    }

    /**
     * Create an aliased <code>stevedb.address</code> table reference
     */
    public Address(Name alias) {
        this(alias, ADDRESS);
    }

    /**
     * Create a <code>stevedb.address</code> table reference
     */
    public Address() {
        this(DSL.name("address"), null);
    }

    public <O extends Record> Address(Table<O> path, ForeignKey<O, AddressRecord> childPath, InverseForeignKey<O, AddressRecord> parentPath) {
        super(path, childPath, parentPath, ADDRESS);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class AddressPath extends Address implements Path<AddressRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> AddressPath(Table<O> path, ForeignKey<O, AddressRecord> childPath, InverseForeignKey<O, AddressRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private AddressPath(Name alias, Table<AddressRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public AddressPath as(String alias) {
            return new AddressPath(DSL.name(alias), this);
        }

        @Override
        public AddressPath as(Name alias) {
            return new AddressPath(alias, this);
        }

        @Override
        public AddressPath as(Table<?> alias) {
            return new AddressPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public Identity<AddressRecord, Integer> getIdentity() {
        return (Identity<AddressRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<AddressRecord> getPrimaryKey() {
        return Keys.KEY_ADDRESS_PRIMARY;
    }

    private transient ChargeBoxPath _chargeBox;

    /**
     * Get the implicit to-many join path to the <code>stevedb.charge_box</code>
     * table
     */
    public ChargeBoxPath chargeBox() {
        if (_chargeBox == null)
            _chargeBox = new ChargeBoxPath(this, null, Keys.FK_CHARGE_BOX_ADDRESS_APK.getInverseKey());

        return _chargeBox;
    }

    private transient UserPath _user;

    /**
     * Get the implicit to-many join path to the <code>stevedb.user</code> table
     */
    public UserPath user() {
        if (_user == null)
            _user = new UserPath(this, null, Keys.FK_USER_ADDRESS_APK.getInverseKey());

        return _user;
    }

    @Override
    public Address as(String alias) {
        return new Address(DSL.name(alias), this);
    }

    @Override
    public Address as(Name alias) {
        return new Address(alias, this);
    }

    @Override
    public Address as(Table<?> alias) {
        return new Address(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public Address rename(String name) {
        return new Address(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Address rename(Name name) {
        return new Address(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public Address rename(Table<?> name) {
        return new Address(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Address where(Condition condition) {
        return new Address(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Address where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Address where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Address where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Address where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Address where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Address where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Address where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Address whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Address whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
