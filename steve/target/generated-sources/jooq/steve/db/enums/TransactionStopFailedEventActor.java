/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.enums;


import org.jooq.Catalog;
import org.jooq.EnumType;
import org.jooq.Schema;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public enum TransactionStopFailedEventActor implements EnumType {

    station("station"),

    manual("manual");

    private final String literal;

    private TransactionStopFailedEventActor(String literal) {
        this.literal = literal;
    }

    @Override
    public Catalog getCatalog() {
        return null;
    }

    @Override
    public Schema getSchema() {
        return null;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getLiteral() {
        return literal;
    }

    /**
     * Lookup a value of this EnumType by its literal. Returns
     * <code>null</code>, if no such value could be found, see {@link
     * EnumType#lookupLiteral(Class, String)}.
     */
    public static TransactionStopFailedEventActor lookupLiteral(String literal) {
        return EnumType.lookupLiteral(TransactionStopFailedEventActor.class, literal);
    }
}
