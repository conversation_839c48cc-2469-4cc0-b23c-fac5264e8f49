/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables;


import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jooq.steve.db.Keys;
import jooq.steve.db.Stevedb;
import jooq.steve.db.tables.ChargeBox.ChargeBoxPath;
import jooq.steve.db.tables.ChargingProfile.ChargingProfilePath;
import jooq.steve.db.tables.ConnectorChargingProfile.ConnectorChargingProfilePath;
import jooq.steve.db.tables.ConnectorMeterValue.ConnectorMeterValuePath;
import jooq.steve.db.tables.ConnectorStatus.ConnectorStatusPath;
import jooq.steve.db.tables.Reservation.ReservationPath;
import jooq.steve.db.tables.TransactionStart.TransactionStartPath;
import jooq.steve.db.tables.records.ConnectorRecord;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.InverseForeignKey;
import org.jooq.Name;
import org.jooq.Path;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Connector extends TableImpl<ConnectorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>stevedb.connector</code>
     */
    public static final Connector CONNECTOR = new Connector();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConnectorRecord> getRecordType() {
        return ConnectorRecord.class;
    }

    /**
     * The column <code>stevedb.connector.connector_pk</code>.
     */
    public final TableField<ConnectorRecord, Integer> CONNECTOR_PK = createField(DSL.name("connector_pk"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>stevedb.connector.charge_box_id</code>.
     */
    public final TableField<ConnectorRecord, String> CHARGE_BOX_ID = createField(DSL.name("charge_box_id"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>stevedb.connector.connector_id</code>.
     */
    public final TableField<ConnectorRecord, Integer> CONNECTOR_ID = createField(DSL.name("connector_id"), SQLDataType.INTEGER.nullable(false), this, "");

    private Connector(Name alias, Table<ConnectorRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private Connector(Name alias, Table<ConnectorRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased <code>stevedb.connector</code> table reference
     */
    public Connector(String alias) {
        this(DSL.name(alias), CONNECTOR);
    }

    /**
     * Create an aliased <code>stevedb.connector</code> table reference
     */
    public Connector(Name alias) {
        this(alias, CONNECTOR);
    }

    /**
     * Create a <code>stevedb.connector</code> table reference
     */
    public Connector() {
        this(DSL.name("connector"), null);
    }

    public <O extends Record> Connector(Table<O> path, ForeignKey<O, ConnectorRecord> childPath, InverseForeignKey<O, ConnectorRecord> parentPath) {
        super(path, childPath, parentPath, CONNECTOR);
    }

    /**
     * A subtype implementing {@link Path} for simplified path-based joins.
     */
    public static class ConnectorPath extends Connector implements Path<ConnectorRecord> {

        private static final long serialVersionUID = 1L;
        public <O extends Record> ConnectorPath(Table<O> path, ForeignKey<O, ConnectorRecord> childPath, InverseForeignKey<O, ConnectorRecord> parentPath) {
            super(path, childPath, parentPath);
        }
        private ConnectorPath(Name alias, Table<ConnectorRecord> aliased) {
            super(alias, aliased);
        }

        @Override
        public ConnectorPath as(String alias) {
            return new ConnectorPath(DSL.name(alias), this);
        }

        @Override
        public ConnectorPath as(Name alias) {
            return new ConnectorPath(alias, this);
        }

        @Override
        public ConnectorPath as(Table<?> alias) {
            return new ConnectorPath(alias.getQualifiedName(), this);
        }
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Stevedb.STEVEDB;
    }

    @Override
    public Identity<ConnectorRecord, Integer> getIdentity() {
        return (Identity<ConnectorRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<ConnectorRecord> getPrimaryKey() {
        return Keys.KEY_CONNECTOR_PRIMARY;
    }

    @Override
    public List<UniqueKey<ConnectorRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_CONNECTOR_CONNECTOR_CBID_CID_UNIQUE, Keys.KEY_CONNECTOR_CONNECTOR_PK_UNIQUE);
    }

    @Override
    public List<ForeignKey<ConnectorRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FK_CONNECTOR_CHARGE_BOX_CBID);
    }

    private transient ChargeBoxPath _chargeBox;

    /**
     * Get the implicit join path to the <code>stevedb.charge_box</code> table.
     */
    public ChargeBoxPath chargeBox() {
        if (_chargeBox == null)
            _chargeBox = new ChargeBoxPath(this, Keys.FK_CONNECTOR_CHARGE_BOX_CBID, null);

        return _chargeBox;
    }

    private transient ConnectorChargingProfilePath _connectorChargingProfile;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.connector_charging_profile</code> table
     */
    public ConnectorChargingProfilePath connectorChargingProfile() {
        if (_connectorChargingProfile == null)
            _connectorChargingProfile = new ConnectorChargingProfilePath(this, null, Keys.FK_CONNECTOR_CHARGING_PROFILE_CONNECTOR_PK.getInverseKey());

        return _connectorChargingProfile;
    }

    private transient ReservationPath _reservation;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.reservation</code> table
     */
    public ReservationPath reservation() {
        if (_reservation == null)
            _reservation = new ReservationPath(this, null, Keys.FK_CONNECTOR_PK_RESERV.getInverseKey());

        return _reservation;
    }

    private transient TransactionStartPath _transactionStart;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.transaction_start</code> table
     */
    public TransactionStartPath transactionStart() {
        if (_transactionStart == null)
            _transactionStart = new TransactionStartPath(this, null, Keys.FK_CONNECTOR_PK_T.getInverseKey());

        return _transactionStart;
    }

    private transient ConnectorStatusPath _connectorStatus;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.connector_status</code> table
     */
    public ConnectorStatusPath connectorStatus() {
        if (_connectorStatus == null)
            _connectorStatus = new ConnectorStatusPath(this, null, Keys.FK_CS_PK.getInverseKey());

        return _connectorStatus;
    }

    private transient ConnectorMeterValuePath _connectorMeterValue;

    /**
     * Get the implicit to-many join path to the
     * <code>stevedb.connector_meter_value</code> table
     */
    public ConnectorMeterValuePath connectorMeterValue() {
        if (_connectorMeterValue == null)
            _connectorMeterValue = new ConnectorMeterValuePath(this, null, Keys.FK_PK_CM.getInverseKey());

        return _connectorMeterValue;
    }

    /**
     * Get the implicit many-to-many join path to the
     * <code>stevedb.charging_profile</code> table
     */
    public ChargingProfilePath chargingProfile() {
        return connectorChargingProfile().chargingProfile();
    }

    @Override
    public Connector as(String alias) {
        return new Connector(DSL.name(alias), this);
    }

    @Override
    public Connector as(Name alias) {
        return new Connector(alias, this);
    }

    @Override
    public Connector as(Table<?> alias) {
        return new Connector(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public Connector rename(String name) {
        return new Connector(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Connector rename(Name name) {
        return new Connector(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public Connector rename(Table<?> name) {
        return new Connector(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Connector where(Condition condition) {
        return new Connector(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Connector where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Connector where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Connector where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Connector where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Connector where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Connector where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public Connector where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Connector whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public Connector whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
