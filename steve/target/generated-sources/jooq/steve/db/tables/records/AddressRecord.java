/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.Address;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AddressRecord extends UpdatableRecordImpl<AddressRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.address.address_pk</code>.
     */
    public AddressRecord setAddressPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.address.address_pk</code>.
     */
    public Integer getAddressPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.address.street</code>.
     */
    public AddressRecord setStreet(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.address.street</code>.
     */
    public String getStreet() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.address.house_number</code>.
     */
    public AddressRecord setHouseNumber(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.address.house_number</code>.
     */
    public String getHouseNumber() {
        return (String) get(2);
    }

    /**
     * Setter for <code>stevedb.address.zip_code</code>.
     */
    public AddressRecord setZipCode(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.address.zip_code</code>.
     */
    public String getZipCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.address.city</code>.
     */
    public AddressRecord setCity(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.address.city</code>.
     */
    public String getCity() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.address.country</code>.
     */
    public AddressRecord setCountry(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.address.country</code>.
     */
    public String getCountry() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AddressRecord
     */
    public AddressRecord() {
        super(Address.ADDRESS);
    }

    /**
     * Create a detached, initialised AddressRecord
     */
    public AddressRecord(Integer addressPk, String street, String houseNumber, String zipCode, String city, String country) {
        super(Address.ADDRESS);

        setAddressPk(addressPk);
        setStreet(street);
        setHouseNumber(houseNumber);
        setZipCode(zipCode);
        setCity(city);
        setCountry(country);
        resetChangedOnNotNull();
    }
}
