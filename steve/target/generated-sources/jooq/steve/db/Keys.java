/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db;


import jooq.steve.db.tables.Address;
import jooq.steve.db.tables.ChargeBox;
import jooq.steve.db.tables.ChargingProfile;
import jooq.steve.db.tables.ChargingSchedulePeriod;
import jooq.steve.db.tables.Connector;
import jooq.steve.db.tables.ConnectorChargingProfile;
import jooq.steve.db.tables.ConnectorMeterValue;
import jooq.steve.db.tables.ConnectorStatus;
import jooq.steve.db.tables.OcppTag;
import jooq.steve.db.tables.Reservation;
import jooq.steve.db.tables.SchemaVersion;
import jooq.steve.db.tables.Settings;
import jooq.steve.db.tables.TransactionStart;
import jooq.steve.db.tables.TransactionStop;
import jooq.steve.db.tables.User;
import jooq.steve.db.tables.WebUser;
import jooq.steve.db.tables.records.AddressRecord;
import jooq.steve.db.tables.records.ChargeBoxRecord;
import jooq.steve.db.tables.records.ChargingProfileRecord;
import jooq.steve.db.tables.records.ChargingSchedulePeriodRecord;
import jooq.steve.db.tables.records.ConnectorChargingProfileRecord;
import jooq.steve.db.tables.records.ConnectorMeterValueRecord;
import jooq.steve.db.tables.records.ConnectorRecord;
import jooq.steve.db.tables.records.ConnectorStatusRecord;
import jooq.steve.db.tables.records.OcppTagRecord;
import jooq.steve.db.tables.records.ReservationRecord;
import jooq.steve.db.tables.records.SchemaVersionRecord;
import jooq.steve.db.tables.records.SettingsRecord;
import jooq.steve.db.tables.records.TransactionStartRecord;
import jooq.steve.db.tables.records.TransactionStopRecord;
import jooq.steve.db.tables.records.UserRecord;
import jooq.steve.db.tables.records.WebUserRecord;

import org.jooq.ForeignKey;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in
 * stevedb.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<AddressRecord> KEY_ADDRESS_PRIMARY = Internal.createUniqueKey(Address.ADDRESS, DSL.name("KEY_address_PRIMARY"), new TableField[] { Address.ADDRESS.ADDRESS_PK }, true);
    public static final UniqueKey<ChargeBoxRecord> KEY_CHARGE_BOX_CHARGEBOXID_UNIQUE = Internal.createUniqueKey(ChargeBox.CHARGE_BOX, DSL.name("KEY_charge_box_chargeBoxId_UNIQUE"), new TableField[] { ChargeBox.CHARGE_BOX.CHARGE_BOX_ID }, true);
    public static final UniqueKey<ChargeBoxRecord> KEY_CHARGE_BOX_PRIMARY = Internal.createUniqueKey(ChargeBox.CHARGE_BOX, DSL.name("KEY_charge_box_PRIMARY"), new TableField[] { ChargeBox.CHARGE_BOX.CHARGE_BOX_PK }, true);
    public static final UniqueKey<ChargingProfileRecord> KEY_CHARGING_PROFILE_PRIMARY = Internal.createUniqueKey(ChargingProfile.CHARGING_PROFILE, DSL.name("KEY_charging_profile_PRIMARY"), new TableField[] { ChargingProfile.CHARGING_PROFILE.CHARGING_PROFILE_PK }, true);
    public static final UniqueKey<ChargingSchedulePeriodRecord> KEY_CHARGING_SCHEDULE_PERIOD_UQ_CHARGING_SCHEDULE_PERIOD = Internal.createUniqueKey(ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD, DSL.name("KEY_charging_schedule_period_UQ_charging_schedule_period"), new TableField[] { ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD.CHARGING_PROFILE_PK, ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD.START_PERIOD_IN_SECONDS }, true);
    public static final UniqueKey<ConnectorRecord> KEY_CONNECTOR_CONNECTOR_CBID_CID_UNIQUE = Internal.createUniqueKey(Connector.CONNECTOR, DSL.name("KEY_connector_connector_cbid_cid_UNIQUE"), new TableField[] { Connector.CONNECTOR.CHARGE_BOX_ID, Connector.CONNECTOR.CONNECTOR_ID }, true);
    public static final UniqueKey<ConnectorRecord> KEY_CONNECTOR_CONNECTOR_PK_UNIQUE = Internal.createUniqueKey(Connector.CONNECTOR, DSL.name("KEY_connector_connector_pk_UNIQUE"), new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final UniqueKey<ConnectorRecord> KEY_CONNECTOR_PRIMARY = Internal.createUniqueKey(Connector.CONNECTOR, DSL.name("KEY_connector_PRIMARY"), new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final UniqueKey<ConnectorChargingProfileRecord> KEY_CONNECTOR_CHARGING_PROFILE_UQ_CONNECTOR_CHARGING_PROFILE = Internal.createUniqueKey(ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE, DSL.name("KEY_connector_charging_profile_UQ_connector_charging_profile"), new TableField[] { ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE.CONNECTOR_PK, ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE.CHARGING_PROFILE_PK }, true);
    public static final UniqueKey<OcppTagRecord> KEY_OCPP_TAG_IDTAG_UNIQUE = Internal.createUniqueKey(OcppTag.OCPP_TAG, DSL.name("KEY_ocpp_tag_idTag_UNIQUE"), new TableField[] { OcppTag.OCPP_TAG.ID_TAG }, true);
    public static final UniqueKey<OcppTagRecord> KEY_OCPP_TAG_PRIMARY = Internal.createUniqueKey(OcppTag.OCPP_TAG, DSL.name("KEY_ocpp_tag_PRIMARY"), new TableField[] { OcppTag.OCPP_TAG.OCPP_TAG_PK }, true);
    public static final UniqueKey<ReservationRecord> KEY_RESERVATION_PRIMARY = Internal.createUniqueKey(Reservation.RESERVATION, DSL.name("KEY_reservation_PRIMARY"), new TableField[] { Reservation.RESERVATION.RESERVATION_PK }, true);
    public static final UniqueKey<ReservationRecord> KEY_RESERVATION_RESERVATION_PK_UNIQUE = Internal.createUniqueKey(Reservation.RESERVATION, DSL.name("KEY_reservation_reservation_pk_UNIQUE"), new TableField[] { Reservation.RESERVATION.RESERVATION_PK }, true);
    public static final UniqueKey<ReservationRecord> KEY_RESERVATION_TRANSACTION_PK_UNIQUE = Internal.createUniqueKey(Reservation.RESERVATION, DSL.name("KEY_reservation_transaction_pk_UNIQUE"), new TableField[] { Reservation.RESERVATION.TRANSACTION_PK }, true);
    public static final UniqueKey<SchemaVersionRecord> KEY_SCHEMA_VERSION_PRIMARY = Internal.createUniqueKey(SchemaVersion.SCHEMA_VERSION, DSL.name("KEY_schema_version_PRIMARY"), new TableField[] { SchemaVersion.SCHEMA_VERSION.INSTALLED_RANK }, true);
    public static final UniqueKey<SettingsRecord> KEY_SETTINGS_PRIMARY = Internal.createUniqueKey(Settings.SETTINGS, DSL.name("KEY_settings_PRIMARY"), new TableField[] { Settings.SETTINGS.APP_ID }, true);
    public static final UniqueKey<SettingsRecord> KEY_SETTINGS_SETTINGS_ID_UNIQUE = Internal.createUniqueKey(Settings.SETTINGS, DSL.name("KEY_settings_settings_id_UNIQUE"), new TableField[] { Settings.SETTINGS.APP_ID }, true);
    public static final UniqueKey<TransactionStartRecord> KEY_TRANSACTION_START_PRIMARY = Internal.createUniqueKey(TransactionStart.TRANSACTION_START, DSL.name("KEY_transaction_start_PRIMARY"), new TableField[] { TransactionStart.TRANSACTION_START.TRANSACTION_PK }, true);
    public static final UniqueKey<TransactionStartRecord> KEY_TRANSACTION_START_TRANSACTION_PK_UNIQUE = Internal.createUniqueKey(TransactionStart.TRANSACTION_START, DSL.name("KEY_transaction_start_transaction_pk_UNIQUE"), new TableField[] { TransactionStart.TRANSACTION_START.TRANSACTION_PK }, true);
    public static final UniqueKey<TransactionStopRecord> KEY_TRANSACTION_STOP_PRIMARY = Internal.createUniqueKey(TransactionStop.TRANSACTION_STOP, DSL.name("KEY_transaction_stop_PRIMARY"), new TableField[] { TransactionStop.TRANSACTION_STOP.TRANSACTION_PK, TransactionStop.TRANSACTION_STOP.EVENT_TIMESTAMP }, true);
    public static final UniqueKey<UserRecord> KEY_USER_PRIMARY = Internal.createUniqueKey(User.USER, DSL.name("KEY_user_PRIMARY"), new TableField[] { User.USER.USER_PK }, true);
    public static final UniqueKey<WebUserRecord> KEY_WEB_USER_PRIMARY = Internal.createUniqueKey(WebUser.WEB_USER, DSL.name("KEY_web_user_PRIMARY"), new TableField[] { WebUser.WEB_USER.WEB_USER_PK }, true);
    public static final UniqueKey<WebUserRecord> KEY_WEB_USER_USERNAME = Internal.createUniqueKey(WebUser.WEB_USER, DSL.name("KEY_web_user_username"), new TableField[] { WebUser.WEB_USER.USERNAME }, true);

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    public static final ForeignKey<ChargeBoxRecord, AddressRecord> FK_CHARGE_BOX_ADDRESS_APK = Internal.createForeignKey(ChargeBox.CHARGE_BOX, DSL.name("FK_charge_box_address_apk"), new TableField[] { ChargeBox.CHARGE_BOX.ADDRESS_PK }, Keys.KEY_ADDRESS_PRIMARY, new TableField[] { Address.ADDRESS.ADDRESS_PK }, true);
    public static final ForeignKey<ChargingSchedulePeriodRecord, ChargingProfileRecord> FK_CHARGING_SCHEDULE_PERIOD_CHARGING_PROFILE_PK = Internal.createForeignKey(ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD, DSL.name("FK_charging_schedule_period_charging_profile_pk"), new TableField[] { ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD.CHARGING_PROFILE_PK }, Keys.KEY_CHARGING_PROFILE_PRIMARY, new TableField[] { ChargingProfile.CHARGING_PROFILE.CHARGING_PROFILE_PK }, true);
    public static final ForeignKey<ConnectorRecord, ChargeBoxRecord> FK_CONNECTOR_CHARGE_BOX_CBID = Internal.createForeignKey(Connector.CONNECTOR, DSL.name("FK_connector_charge_box_cbid"), new TableField[] { Connector.CONNECTOR.CHARGE_BOX_ID }, Keys.KEY_CHARGE_BOX_CHARGEBOXID_UNIQUE, new TableField[] { ChargeBox.CHARGE_BOX.CHARGE_BOX_ID }, true);
    public static final ForeignKey<ConnectorChargingProfileRecord, ChargingProfileRecord> FK_CONNECTOR_CHARGING_PROFILE_CHARGING_PROFILE_PK = Internal.createForeignKey(ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE, DSL.name("FK_connector_charging_profile_charging_profile_pk"), new TableField[] { ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE.CHARGING_PROFILE_PK }, Keys.KEY_CHARGING_PROFILE_PRIMARY, new TableField[] { ChargingProfile.CHARGING_PROFILE.CHARGING_PROFILE_PK }, true);
    public static final ForeignKey<ConnectorChargingProfileRecord, ConnectorRecord> FK_CONNECTOR_CHARGING_PROFILE_CONNECTOR_PK = Internal.createForeignKey(ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE, DSL.name("FK_connector_charging_profile_connector_pk"), new TableField[] { ConnectorChargingProfile.CONNECTOR_CHARGING_PROFILE.CONNECTOR_PK }, Keys.KEY_CONNECTOR_PRIMARY, new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final ForeignKey<ConnectorMeterValueRecord, ConnectorRecord> FK_PK_CM = Internal.createForeignKey(ConnectorMeterValue.CONNECTOR_METER_VALUE, DSL.name("FK_pk_cm"), new TableField[] { ConnectorMeterValue.CONNECTOR_METER_VALUE.CONNECTOR_PK }, Keys.KEY_CONNECTOR_PRIMARY, new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final ForeignKey<ConnectorMeterValueRecord, TransactionStartRecord> FK_TID_CM = Internal.createForeignKey(ConnectorMeterValue.CONNECTOR_METER_VALUE, DSL.name("FK_tid_cm"), new TableField[] { ConnectorMeterValue.CONNECTOR_METER_VALUE.TRANSACTION_PK }, Keys.KEY_TRANSACTION_START_PRIMARY, new TableField[] { TransactionStart.TRANSACTION_START.TRANSACTION_PK }, true);
    public static final ForeignKey<ConnectorStatusRecord, ConnectorRecord> FK_CS_PK = Internal.createForeignKey(ConnectorStatus.CONNECTOR_STATUS, DSL.name("FK_cs_pk"), new TableField[] { ConnectorStatus.CONNECTOR_STATUS.CONNECTOR_PK }, Keys.KEY_CONNECTOR_PRIMARY, new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final ForeignKey<OcppTagRecord, OcppTagRecord> FK_OCPP_TAG_PARENT_ID_TAG = Internal.createForeignKey(OcppTag.OCPP_TAG, DSL.name("FK_ocpp_tag_parent_id_tag"), new TableField[] { OcppTag.OCPP_TAG.PARENT_ID_TAG }, Keys.KEY_OCPP_TAG_IDTAG_UNIQUE, new TableField[] { OcppTag.OCPP_TAG.ID_TAG }, true);
    public static final ForeignKey<ReservationRecord, ConnectorRecord> FK_CONNECTOR_PK_RESERV = Internal.createForeignKey(Reservation.RESERVATION, DSL.name("FK_connector_pk_reserv"), new TableField[] { Reservation.RESERVATION.CONNECTOR_PK }, Keys.KEY_CONNECTOR_PRIMARY, new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final ForeignKey<ReservationRecord, OcppTagRecord> FK_RESERVATION_OCPP_TAG_ID_TAG = Internal.createForeignKey(Reservation.RESERVATION, DSL.name("FK_reservation_ocpp_tag_id_tag"), new TableField[] { Reservation.RESERVATION.ID_TAG }, Keys.KEY_OCPP_TAG_IDTAG_UNIQUE, new TableField[] { OcppTag.OCPP_TAG.ID_TAG }, true);
    public static final ForeignKey<ReservationRecord, TransactionStartRecord> FK_TRANSACTION_PK_R = Internal.createForeignKey(Reservation.RESERVATION, DSL.name("FK_transaction_pk_r"), new TableField[] { Reservation.RESERVATION.TRANSACTION_PK }, Keys.KEY_TRANSACTION_START_PRIMARY, new TableField[] { TransactionStart.TRANSACTION_START.TRANSACTION_PK }, true);
    public static final ForeignKey<TransactionStartRecord, ConnectorRecord> FK_CONNECTOR_PK_T = Internal.createForeignKey(TransactionStart.TRANSACTION_START, DSL.name("FK_connector_pk_t"), new TableField[] { TransactionStart.TRANSACTION_START.CONNECTOR_PK }, Keys.KEY_CONNECTOR_PRIMARY, new TableField[] { Connector.CONNECTOR.CONNECTOR_PK }, true);
    public static final ForeignKey<TransactionStartRecord, OcppTagRecord> FK_TRANSACTION_OCPP_TAG_ID_TAG = Internal.createForeignKey(TransactionStart.TRANSACTION_START, DSL.name("FK_transaction_ocpp_tag_id_tag"), new TableField[] { TransactionStart.TRANSACTION_START.ID_TAG }, Keys.KEY_OCPP_TAG_IDTAG_UNIQUE, new TableField[] { OcppTag.OCPP_TAG.ID_TAG }, true);
    public static final ForeignKey<TransactionStopRecord, TransactionStartRecord> FK_TRANSACTION_STOP_TRANSACTION_PK = Internal.createForeignKey(TransactionStop.TRANSACTION_STOP, DSL.name("FK_transaction_stop_transaction_pk"), new TableField[] { TransactionStop.TRANSACTION_STOP.TRANSACTION_PK }, Keys.KEY_TRANSACTION_START_PRIMARY, new TableField[] { TransactionStart.TRANSACTION_START.TRANSACTION_PK }, true);
    public static final ForeignKey<UserRecord, AddressRecord> FK_USER_ADDRESS_APK = Internal.createForeignKey(User.USER, DSL.name("FK_user_address_apk"), new TableField[] { User.USER.ADDRESS_PK }, Keys.KEY_ADDRESS_PRIMARY, new TableField[] { Address.ADDRESS.ADDRESS_PK }, true);
    public static final ForeignKey<UserRecord, OcppTagRecord> FK_USER_OCPP_TAG_OTPK = Internal.createForeignKey(User.USER, DSL.name("FK_user_ocpp_tag_otpk"), new TableField[] { User.USER.OCPP_TAG_PK }, Keys.KEY_OCPP_TAG_PRIMARY, new TableField[] { OcppTag.OCPP_TAG.OCPP_TAG_PK }, true);
}
