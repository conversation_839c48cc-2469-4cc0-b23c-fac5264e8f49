@charset "UTF-8";
@font-face{font-family:DroidSerif; src:url(DroidSerif.ttf);}

html, body { height: 100%; overflow: auto; }
#diffElements span a:link, #diffElements span  span a:visited { color:#000; text-decoration:none; cursor: default;}
#diffElements span a:hover, #diffElements span a:active { color:#ce2828; text-decoration:none; cursor: default;}
#diffElements input, #diffElements select {width: 130px;}
a:link, a:visited {color: #000; text-decoration: underline}
a:hover, a:active {color: #af6021; text-decoration: underline}

body {
	font-family: Verdana, sans-serif;
	font-size: 12px;
	background: #e0e6e9;
	padding: 0;
	margin: 0;
}
section {
	margin-bottom: 12px;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAytpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MEI2QkFDMDQ3MDA1MTFFMzhFRTdFMUM3MjQzQjM3QzEiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MEI2QkFDMDM3MDA1MTFFMzhFRTdFMUM3MjQzQjM3QzEiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChNYWNpbnRvc2gpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5kaWQ6MTczM2IzZGQtYTIxNS00MDk4LThhM2EtYzE1MzkyZGUzZmQwIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjE3MzNiM2RkLWEyMTUtNDA5OC04YTNhLWMxNTM5MmRlM2ZkMCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Ptw6u3kAAAAPSURBVHjaYjhz5gxAgAEABMwCZTQYvMQAAAAASUVORK5CYII=') repeat-x 50% 50%;
}
section span { color:#5c5c5c; background:#fff; padding-right:10px; font-family:DroidSerif; font-size: 14px; font-weight: normal;}
ul hr { color: #CCC; background: #CCC; 	height: 1px; border: 0;}
table { width: 100%;}
table.action td:last-child {
	border-left: 1px solid #CCC;
}
table.action th:last-child {
	border-left: 1px solid #CCC;
}
table.userInput th:first-child {
	text-align: right;
}
table.userInput td:first-child {
	text-align: right;
	width: 45%;
	padding: 3px;
}
table.userInputFullPage td:first-child {width: 50%; text-align: right; padding: 3px;}
table.sll { padding-bottom: 40px; }
table.res {
	border-collapse: collapse;
	table-layout:fixed;
}
table.res th {
	text-align: center;
	padding: 5px;
	background: #fff;
	border-bottom: double #CCC;
}
table.res th[data-sort] {
	cursor:pointer;
}
table.res tr { border-bottom: 1px solid #CCC; }
table.res tr:nth-child(odd){ background: #fcfaf2; }

table.res tr:nth-child(even):hover {  background: #ebf4f9; }
table.res tr:nth-child(odd):hover {  background: #ebf4f9; }

table.res td {
	padding: 5px;
	text-align: center;
	width: auto;
	word-wrap: break-word;
}
/*** Table for charge point details ***/
table.cpd {
	border-collapse: collapse;
	width: 70%;
}
table.cpd th {
	text-align: left;
	padding: 5px 5px 5px 15px;
	background: #fff;
	border-bottom: double #CCC;
}
table.cpd tr { 
	border-bottom: 1px solid #CCC; 
}
table.cpd td {
	padding: 5px;
	text-align: center;
}
table.cpd td:first-child {
	text-align: left;
	width: 200px;
	padding-left: 15px;
	background: #f7f7f7;
}
/*** Fin ***/
select {
	width: 70%;
	font-size: 12px;
	margin: 1px 0 1px 0;
	padding: 2px;
	outline: none;
	border: 1px solid #CCC;
	border-radius: 0;
  	-webkit-border-radius: 0;
}
select option { font-size: 12px; }
input[type="text"], input[type="number"], input[type="password"], input[type="text"].dateTimePicker{
	appearance: none;
	-webkit-appearance: none;
	margin: 1px 3px 1px 0;
	padding: 3px;
	border: 1px solid #CCC;
	border-radius: 0;
 	width: 70%;
	font-size: 12px;
}

input[type="button"] {
	margin: 0 0 5px 5px;
    width: 100px;
	font-size:12px;
	height: 25px;
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
	border-radius:3px;
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #ededed), color-stop(1, #dfdfdf));
	background:-moz-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:-webkit-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:-o-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:-ms-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:linear-gradient(to bottom, #ededed 5%, #dfdfdf 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ededed', endColorstr='#dfdfdf',GradientType=0);
	background-color:#ededed;
	border:1px solid #b0b0b0;
	display:inline-block;
	color:#575757;
	text-decoration:none;
}
input[type="button"]:hover {
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #dfdfdf), color-stop(1, #ededed));
	background:-moz-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:-webkit-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:-o-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:-ms-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:linear-gradient(to bottom, #dfdfdf 5%, #ededed 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dfdfdf', endColorstr='#ededed',GradientType=0);
	background-color:#dfdfdf;
}
input[type="button"]:active, input[type="submit"]:active {
	position:relative;
	top:1px;
}
input[type="submit"] {
	min-width: 100px;
	font-size: 12px;
	background: #397079;
	color: #fff;
    cursor: pointer;
	height: 25px;
	border: 0;
	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}
input[type="submit"]:hover {
	background:#29575f;
}
input[type="submit"].blueSubmit {
	background:#397ac2;
}
input[type="submit"].blueSubmit:hover {
	background:#1e63b0;
}
input[type="submit"].redSubmit {
	background:#c14848;
}
input[type="submit"].redSubmit:hover {
	background:#af3232;
}
textarea {
	margin: 1px 3px 1px 0;
	padding: 3px;
	border: 1px solid #CCC;
	border-radius: 0;
	width: 70%;
	height: 6em; /* 6 rows */
	font-size: 12px;
	font-family: Verdana, sans-serif;
	resize: vertical;
}
#add_space { padding-top: 20px; }
.add-margin-bottom { margin-bottom: 20px; }
/*************** top menu navigation div ***************/
ul.navigation {
	color: #ccc;
	margin:0;
	position:relative;
	float:right;
}
ul.navigation li {
	display:inline;
	margin:0;
	padding:0;
	float:left;
	position:relative;
}

ul.navigation li a {
	padding-left:10px;
	padding-right:10px;
	line-height: 35px;
	text-decoration:none;
	display:inline-block;
	cursor: pointer;
}
ul.navigation li a:link, ul.navigation li a:visited { color:#CCC; }
ul.navigation li a:hover, ul.navigation li a:active { color:#fff; }
ul.navigation li:hover > ul {
	visibility:visible;
	opacity:1;
}
ul.navigation ul {
	list-style: none;
    margin: 0;
    padding: 0;    
    visibility:hidden;
    opacity:0;
    position: absolute;
    z-index: 99999;
    text-align: center;
}
ul.navigation ul li {
	background:#000;
	width:100%;
}
ul.navigation ul li a {
	text-decoration:none;
	display:inline-block;
    width:100%;
    padding: 0;
}
/*************** fin ***************/
.left-menu {
	float: left;
	width: 230px;
	height: 100%;
	margin: 0;
	padding: 0; 
	list-style-type: none;
}
.left-menu ul {
	list-style: none;
	margin: 0;
	padding: 0;
}
.left-menu li a {
	color:#000;
	display: block;
	voice-family: inherit;
	text-decoration: none;
    outline: none;
	background: #fff;
	padding: 7px;
}
.left-menu li a:link{
	color:#000;
	background: #fff;
}
.left-menu li a:hover{
	color:#000;
	background: #cddecf;
	border-radius: 2px 0 0 2px;
}
.left-menu li a.highlight {
	color:#000;
	background: #CCC;
	border-radius: 2px 0 0 2px;
}
.op-content {
	min-height: 350px;
	margin: 0 0 0 230px;
	padding: 0 0 0 12px;
	border-left: 1px solid #CCC;
}
.op15-content {
	min-height: 470px;
	margin: 0 0 0 230px;
	padding: 0 0 0 12px;
	border-left: 1px solid #CCC;
}
.op16-content {
	min-height: 650px;
	margin: 0 0 0 230px;
	padding: 0 0 0 12px;
	border-left: 1px solid #CCC;
}
.main { height: auto; min-height: 100%; }
.main-wrapper {
	height: auto; 
}
.top-banner {
	width: 100%;
	height: 80px;
	border-radius: 0;
	background: #71797d;
}
.top-menu {
	height: 35px;
	border-radius: 0;
	background: #000;
}
.container {
	width: 80%;
	margin-right: auto;
	margin-left: auto;
	*zoom: 1;
}
.content {
	position: relative;
	width: 80%;
	border: 1px solid #CCC;
	border-radius: 4px;
	background: #fff;
	margin: 12px auto;
	padding: 12px;
/* 	overflow: auto; so the size of the wrapper is always the size of the longest content */
}
.submit-button {
	margin-top: 20px;
}
.right-content { margin: 0 0 0 230px; border-left: 1px solid #CCC; padding-left:12px; }
.right-content div { display:none; }
.right-content div:first-child { display:block; }
.info, .warning, .error { text-align: left; border: 1px solid; margin-bottom: 10px; padding:15px 30px 15px 30px; }
.info { color: #00529B; background-color: #daecf4; }
.warning { color: #9F6000; background-color: #FEEFB3; }
.error {
    color: #D8000C;
    background-color: #fdd0d0;
    position: relative;
    width: 80%;
    border: 1px solid #fe9393;
    border-radius: 4px;
    margin: 12px auto;
    padding: 12px;
}
input:disabled { background-color:#dddddd; }
input[type="submit"]:disabled, input[type="submit"]:hover:disabled, input[type="submit"]:active:disabled {
	cursor: not-allowed;
	background:-webkit-gradient(linear, left top, left bottom, from(#a5a5a5), to(#5b5b5b));
	background:-moz-linear-gradient(top,  #a5a5a5,  #5b5b5b);
	background:-o-linear-gradient(top, #a5a5a5, #5b5b5b);
	background:-ms-linear-gradient(top, #a5a5a5, #5b5b5b);
}

a.tooltip { color: #5c5c5c; position: relative; text-decoration: none; }
a.tooltip span { display: none; }
a.tooltip:hover {cursor:default;}
a.tooltip:hover span {
  font-family: Verdana, sans-serif;
  position: absolute;
  z-index: 1001;
  display:block;
  width: 250px;
  top: 2em;
  left: 1em;
  border: 1px solid;
  color: #00529B; background-color: #daecf4;
  font-size: 12px; 
  padding: 0.5em;
}

.tileWrapper { text-align: center; }
.tileRow1 { width: 220px; }

.tileWrapper a:link, .tileWrapper a:visited {
	margin: 10px;
	padding-top: 15px;
	padding-bottom: 15px;
	border: 3px solid #fff;
	border-radius: 4px;
	display: inline-block;
	box-shadow: 2px 2px 5px #969696;
	font-size: 14px;
	font-weight: bold;
	vertical-align: top;
	text-decoration: none;
	background-color: #f7f7f7;
	color: #504f50;
}
.tileWrapper a:hover, .tileWrapper a:active {
	background-color: #eeeeee;
}
span.baseTable { 
	display: table;  
	text-align:left; 
	width:100%; 
	margin-top: 10px;
}
span.baseRow { 
	display: table-row; 
	font-weight: normal;
	line-height: 30px;
}
span.baseCell { 
	display: table-cell; 
}
span.baseCell:first-child {
	width:62%;
	text-align: right; 
}
span.baseCell:nth-child(2) {
	padding-left: 5px;
}
span.base { 
	margin-top: 10px;
	margin-bottom: 10px;
	display: block;
	font-weight: normal;
}
span.formatNumber {
	font-size: 18px;
	color: #a36b83;
	font-weight: bold;
}
table.res th.sorting-asc, table.res th.sorting-desc {
	background: #CCC;
	border-radius: 5px 5px 0 0;
}
input, select, textarea {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.inline {
	display:inline-block;
}
