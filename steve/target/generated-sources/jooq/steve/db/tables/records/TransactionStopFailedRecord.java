/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.enums.TransactionStopFailedEventActor;
import jooq.steve.db.tables.TransactionStopFailed;

import org.joda.time.DateTime;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TransactionStopFailedRecord extends TableRecordImpl<TransactionStopFailedRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.transaction_stop_failed.transaction_pk</code>.
     */
    public TransactionStopFailedRecord setTransactionPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.transaction_pk</code>.
     */
    public Integer getTransactionPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.charge_box_id</code>.
     */
    public TransactionStopFailedRecord setChargeBoxId(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.charge_box_id</code>.
     */
    public String getChargeBoxId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.event_timestamp</code>.
     */
    public TransactionStopFailedRecord setEventTimestamp(DateTime value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.event_timestamp</code>.
     */
    public DateTime getEventTimestamp() {
        return (DateTime) get(2);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.event_actor</code>.
     */
    public TransactionStopFailedRecord setEventActor(TransactionStopFailedEventActor value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.event_actor</code>.
     */
    public TransactionStopFailedEventActor getEventActor() {
        return (TransactionStopFailedEventActor) get(3);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.stop_timestamp</code>.
     */
    public TransactionStopFailedRecord setStopTimestamp(DateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.stop_timestamp</code>.
     */
    public DateTime getStopTimestamp() {
        return (DateTime) get(4);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.stop_value</code>.
     */
    public TransactionStopFailedRecord setStopValue(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.stop_value</code>.
     */
    public String getStopValue() {
        return (String) get(5);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.stop_reason</code>.
     */
    public TransactionStopFailedRecord setStopReason(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.stop_reason</code>.
     */
    public String getStopReason() {
        return (String) get(6);
    }

    /**
     * Setter for <code>stevedb.transaction_stop_failed.fail_reason</code>.
     */
    public TransactionStopFailedRecord setFailReason(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.transaction_stop_failed.fail_reason</code>.
     */
    public String getFailReason() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TransactionStopFailedRecord
     */
    public TransactionStopFailedRecord() {
        super(TransactionStopFailed.TRANSACTION_STOP_FAILED);
    }

    /**
     * Create a detached, initialised TransactionStopFailedRecord
     */
    public TransactionStopFailedRecord(Integer transactionPk, String chargeBoxId, DateTime eventTimestamp, TransactionStopFailedEventActor eventActor, DateTime stopTimestamp, String stopValue, String stopReason, String failReason) {
        super(TransactionStopFailed.TRANSACTION_STOP_FAILED);

        setTransactionPk(transactionPk);
        setChargeBoxId(chargeBoxId);
        setEventTimestamp(eventTimestamp);
        setEventActor(eventActor);
        setStopTimestamp(stopTimestamp);
        setStopValue(stopValue);
        setStopReason(stopReason);
        setFailReason(failReason);
        resetChangedOnNotNull();
    }
}
