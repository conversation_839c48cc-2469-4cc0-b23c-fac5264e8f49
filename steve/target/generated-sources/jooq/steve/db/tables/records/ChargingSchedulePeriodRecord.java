/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import java.math.BigDecimal;

import jooq.steve.db.tables.ChargingSchedulePeriod;

import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChargingSchedulePeriodRecord extends TableRecordImpl<ChargingSchedulePeriodRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for
     * <code>stevedb.charging_schedule_period.charging_profile_pk</code>.
     */
    public ChargingSchedulePeriodRecord setChargingProfilePk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for
     * <code>stevedb.charging_schedule_period.charging_profile_pk</code>.
     */
    public Integer getChargingProfilePk() {
        return (Integer) get(0);
    }

    /**
     * Setter for
     * <code>stevedb.charging_schedule_period.start_period_in_seconds</code>.
     */
    public ChargingSchedulePeriodRecord setStartPeriodInSeconds(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for
     * <code>stevedb.charging_schedule_period.start_period_in_seconds</code>.
     */
    public Integer getStartPeriodInSeconds() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>stevedb.charging_schedule_period.power_limit</code>.
     */
    public ChargingSchedulePeriodRecord setPowerLimit(BigDecimal value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_schedule_period.power_limit</code>.
     */
    public BigDecimal getPowerLimit() {
        return (BigDecimal) get(2);
    }

    /**
     * Setter for <code>stevedb.charging_schedule_period.number_phases</code>.
     */
    public ChargingSchedulePeriodRecord setNumberPhases(Integer value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.charging_schedule_period.number_phases</code>.
     */
    public Integer getNumberPhases() {
        return (Integer) get(3);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ChargingSchedulePeriodRecord
     */
    public ChargingSchedulePeriodRecord() {
        super(ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD);
    }

    /**
     * Create a detached, initialised ChargingSchedulePeriodRecord
     */
    public ChargingSchedulePeriodRecord(Integer chargingProfilePk, Integer startPeriodInSeconds, BigDecimal powerLimit, Integer numberPhases) {
        super(ChargingSchedulePeriod.CHARGING_SCHEDULE_PERIOD);

        setChargingProfilePk(chargingProfilePk);
        setStartPeriodInSeconds(startPeriodInSeconds);
        setPowerLimit(powerLimit);
        setNumberPhases(numberPhases);
        resetChangedOnNotNull();
    }
}
