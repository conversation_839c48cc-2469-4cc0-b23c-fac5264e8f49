/*
 * This file is generated by jOOQ.
 */
package jooq.steve.db.tables.records;


import jooq.steve.db.tables.User;

import org.joda.time.LocalDate;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserRecord extends UpdatableRecordImpl<UserRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>stevedb.user.user_pk</code>.
     */
    public UserRecord setUserPk(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.user_pk</code>.
     */
    public Integer getUserPk() {
        return (Integer) get(0);
    }

    /**
     * Setter for <code>stevedb.user.ocpp_tag_pk</code>.
     */
    public UserRecord setOcppTagPk(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.ocpp_tag_pk</code>.
     */
    public Integer getOcppTagPk() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>stevedb.user.address_pk</code>.
     */
    public UserRecord setAddressPk(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.address_pk</code>.
     */
    public Integer getAddressPk() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>stevedb.user.first_name</code>.
     */
    public UserRecord setFirstName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.first_name</code>.
     */
    public String getFirstName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>stevedb.user.last_name</code>.
     */
    public UserRecord setLastName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.last_name</code>.
     */
    public String getLastName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>stevedb.user.birth_day</code>.
     */
    public UserRecord setBirthDay(LocalDate value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.birth_day</code>.
     */
    public LocalDate getBirthDay() {
        return (LocalDate) get(5);
    }

    /**
     * Setter for <code>stevedb.user.sex</code>.
     */
    public UserRecord setSex(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.sex</code>.
     */
    public String getSex() {
        return (String) get(6);
    }

    /**
     * Setter for <code>stevedb.user.phone</code>.
     */
    public UserRecord setPhone(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.phone</code>.
     */
    public String getPhone() {
        return (String) get(7);
    }

    /**
     * Setter for <code>stevedb.user.e_mail</code>.
     */
    public UserRecord setEMail(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.e_mail</code>.
     */
    public String getEMail() {
        return (String) get(8);
    }

    /**
     * Setter for <code>stevedb.user.note</code>.
     */
    public UserRecord setNote(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>stevedb.user.note</code>.
     */
    public String getNote() {
        return (String) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserRecord
     */
    public UserRecord() {
        super(User.USER);
    }

    /**
     * Create a detached, initialised UserRecord
     */
    public UserRecord(Integer userPk, Integer ocppTagPk, Integer addressPk, String firstName, String lastName, LocalDate birthDay, String sex, String phone, String eMail, String note) {
        super(User.USER);

        setUserPk(userPk);
        setOcppTagPk(ocppTagPk);
        setAddressPk(addressPk);
        setFirstName(firstName);
        setLastName(lastName);
        setBirthDay(birthDay);
        setSex(sex);
        setPhone(phone);
        setEMail(eMail);
        setNote(note);
        resetChangedOnNotNull();
    }
}
